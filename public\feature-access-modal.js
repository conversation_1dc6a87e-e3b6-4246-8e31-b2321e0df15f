/**
 * Feature Access Modal Component
 * A modal that appears when users try to access features that require an active subscription
 */

(function(global) {
  'use strict';

  let isModalInitialized = false;
  let isClosing = false;
  let modalOverlay = null;
  let modalContent = null;
  let currentPromiseResolve = null;
  let redirectToInvite = false;

  /**
   * Creates the modal HTML structure
   * @param {Object} options - Configuration options
   * @returns {string} HTML string for the modal
   */
  function createModalHTML(options) {
    const {
      title = 'Subscription Required',
      message = 'This feature requires an active subscription.',
      confirmText = 'Select Subscription',
      cancelText = 'Not Now',
      featureName = 'feature',
      icon = 'lock', // lock, warning, info
    } = options;

    // Determine icon SVG based on type
    let iconSvg = '';

    if (icon === 'lock') {
      iconSvg = `
        <svg xmlns="http://www.w3.org/2000/svg" class="feature-modal-icon lock" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
        </svg>
      `;
    } else if (icon === 'warning') {
      iconSvg = `
        <svg xmlns="http://www.w3.org/2000/svg" class="feature-modal-icon warning" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
          <line x1="12" y1="9" x2="12" y2="13"></line>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
      `;
    } else if (icon === 'info') {
      iconSvg = `
        <svg xmlns="http://www.w3.org/2000/svg" class="feature-modal-icon info" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="16" x2="12" y2="12"></line>
          <line x1="12" y1="8" x2="12.01" y2="8"></line>
        </svg>
      `;
    }

    return `
      <div class="feature-modal-overlay">
        <div class="feature-modal-content">
          <div class="feature-modal-header">
            ${iconSvg}
            <h2 class="feature-modal-title">${title}</h2>
            <button class="feature-modal-close">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          <div class="feature-modal-body">
            <p class="feature-modal-message">${message}</p>
          </div>
          <div class="feature-modal-footer">
            <button class="feature-modal-cancel-button">${cancelText}</button>
            <button class="feature-modal-confirm-button">${confirmText}</button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Injects CSS styles for the modal
   */
  function injectCSS() {
    if (document.getElementById('feature-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'feature-modal-styles';
    style.textContent = `
      .feature-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.3);
        backdrop-filter: blur(3px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000; /* Higher z-index to appear above loading overlay */
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .feature-modal-content {
        background: #fff;
        border-radius: 0.75rem;
        width: 90%;
        max-width: 450px;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .feature-modal-header {
        display: flex;
        align-items: center;
        padding: 1.25rem 1.5rem 0.75rem;
        position: relative;
      }

      .feature-modal-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 0.75rem;
        flex-shrink: 0;
      }

      .feature-modal-icon.lock {
        color: #1547BB;
      }

      .feature-modal-icon.warning {
        color: #F59E0B;
      }

      .feature-modal-icon.info {
        color: #3B82F6;
      }

      .feature-modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #121C41;
        margin: 0;
        flex-grow: 1;
      }

      .feature-modal-close {
        background: none;
        border: none;
        color: #6B7280;
        cursor: pointer;
        padding: 0.25rem;
        position: absolute;
        top: 1rem;
        right: 1rem;
        transition: color 0.2s;
      }

      .feature-modal-close:hover {
        color: #121C41;
      }

      .feature-modal-body {
        padding: 0.75rem 1.5rem 1.5rem;
      }

      .feature-modal-message {
        margin: 0;
        font-size: 1rem;
        line-height: 1.5;
        color: #4B5563;
      }

      .feature-modal-footer {
        display: flex;
        justify-content: flex-end;
        padding: 1rem 1.5rem;
        background-color: #F9FAFB;
        border-top: 1px solid #E5E7EB;
        gap: 0.75rem;
      }

      .feature-modal-cancel-button {
        padding: 0.5rem 1rem;
        background-color: #F3F4F6;
        color: #4B5563;
        border: 1px solid #D1D5DB;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .feature-modal-cancel-button:hover {
        background-color: #E5E7EB;
      }

      .feature-modal-confirm-button {
        padding: 0.5rem 1rem;
        background-color: #1547BB;
        color: white;
        border: none;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * Initialize event listeners for the modal
   */
  function initializeEventListeners() {
    // Close button
    const closeButton = document.querySelector('.feature-modal-close');
    closeButton.addEventListener('click', () => hideFeatureModal(false));

    // Cancel button
    const cancelButton = document.querySelector('.feature-modal-cancel-button');
    cancelButton.addEventListener('click', () => hideFeatureModal(false));

    // Confirm button
    const confirmButton = document.querySelector('.feature-modal-confirm-button');
    confirmButton.addEventListener('click', () => hideFeatureModal(true));

    // Click outside to close
    modalOverlay.addEventListener('click', (event) => {
      if (event.target === modalOverlay) {
        hideFeatureModal(false);
      }
    });

    // Escape key to close
    document.addEventListener('keydown', handleKeyDown);
  }

  /**
   * Handle keydown events for the modal
   * @param {KeyboardEvent} event - The keyboard event
   */
  function handleKeyDown(event) {
    if (event.key === 'Escape' && isModalInitialized && !isClosing) {
      hideFeatureModal(false);
    }
  }

  /**
   * Shows the feature access modal
   * @param {Object} options - Configuration options
   * @returns {Promise<boolean>} - Always returns true (app is now free)
   */
  function showFeatureModal(options = {}) {
    console.log('Feature access modal bypassed - app is now free');
    return Promise.resolve(true); // Always return true - app is now free

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.feature-modal-overlay');
    if (existingModal) {
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Remove existing keydown listener
    document.removeEventListener('keydown', handleKeyDown);

    // Create modal
    injectCSS();
    const modalHTML = createModalHTML(options);
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.feature-modal-overlay');
    modalContent = document.querySelector('.feature-modal-content');

    // Initialize event listeners
    initializeEventListeners();

    isModalInitialized = true;

    // Show modal with animation
    setTimeout(() => {
      if (isClosing) return;

      modalOverlay.style.opacity = '1';
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);

    return new Promise(resolve => {
      currentPromiseResolve = resolve;
    });
  }

  /**
   * Hides the feature access modal
   * @param {boolean} confirmed - Whether the user confirmed the action
   */
  function hideFeatureModal(confirmed) {
    if (isClosing) return;
    isClosing = true;

    // Remove keydown listener
    document.removeEventListener('keydown', handleKeyDown);

    // Animate closing
    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // If not confirmed and redirectToInvite is true, redirect to invite page
        if (!confirmed && redirectToInvite) {
          // Use the PageLoader if available, otherwise redirect directly
          if (window.PageLoader && typeof window.PageLoader.loadInvitePage === 'function') {
            const mainContent = document.querySelector('#main-content');
            if (mainContent) {
              window.PageLoader.loadInvitePage(mainContent);
              // Update active nav link if the function exists
              if (typeof window.updateActiveNavLink === 'function') {
                window.updateActiveNavLink('invitations');
              }
            }
          }
        }

        // Show subscription modal if confirmed
        if (confirmed && window.SubscriptionModal) {
          window.SubscriptionModal.show(false, false, true);
        }

        // Resolve promise if it exists
        if (currentPromiseResolve) {
          currentPromiseResolve(confirmed);
          currentPromiseResolve = null;
        }
      }, 300);
    }
  }

  // Public API
  global.FeatureAccessModal = {
    show: showFeatureModal,
    hide: hideFeatureModal
  };
})(typeof window !== 'undefined' ? window : global);
