/*******************************************************
 * detail.js
 * Combined code for the Detail Page & Course Recommendations Overlay
 *******************************************************/

// ------------------------------------------------------
// Global Variables for Recommendations (CORE & OTHER)
// ------------------------------------------------------
let courseRecommendations = [];       // Core Only
let otherPathRecommendations = [];     // Other Path Only

//------------------------------------------------------
// LOAD CORE RECOMMENDATIONS (single user)
//------------------------------------------------------
async function loadRecommendations(learner) {
  try {
    const userRef = db
      .collection("companies")
      .doc(userCompany)
      .collection("users")
      .doc(learner.id);

    const userDoc = await userRef.get();
    const userData = userDoc.data();

    courseRecommendations = Array.isArray(userData.courseRecommendations)
      ? userData.courseRecommendations
      : [];
    console.log("Loaded CORE recommendations:", courseRecommendations);
  } catch (error) {
    console.error("Error loading course recommendations:", error);
    courseRecommendations = [];
  }
}

//------------------------------------------------------
// LOAD OTHER PATH RECOMMENDATIONS (single user)
//------------------------------------------------------
async function loadOtherPathRecommendations(learner) {
  try {
    const userRef = db
      .collection("companies")
      .doc(userCompany)
      .collection("users")
      .doc(learner.id);

    const userDoc = await userRef.get();
    const userData = userDoc.data();

    otherPathRecommendations = Array.isArray(userData.otherPathRecommendations)
      ? userData.otherPathRecommendations
      : [];
    console.log("Loaded OTHER path recommendations:", otherPathRecommendations);
  } catch (error) {
    console.error("Error loading OTHER path recommendations:", error);
    otherPathRecommendations = [];
  }
}

//------------------------------------------------------
// CHECK IF ANY AI RECOMMENDATIONS EXIST
//------------------------------------------------------
async function checkAIRecommendations(totalNotEnrolled) {
  try {
    const companyRef = db.collection("companies").doc(userCompany);
    const usersSnapshot = await companyRef.collection("users").get();
    let hasRecommendations = false;

    for (const doc of usersSnapshot.docs) {
      const userData = doc.data();
      // If there's at least one user with at least one CORE recommendation:
      if (
        userData.courseRecommendations &&
        userData.courseRecommendations.length > 0
      ) {
        hasRecommendations = true;
        break;
      }
    }

    return hasRecommendations;
  } catch (error) {
    console.error("Error checking AI recommendations:", error);
    return false;
  }
}

//------------------------------------------------------
// FETCH EMPLOYEE DATA FOR A GIVEN CATEGORY/PATH
//------------------------------------------------------
async function fetchEmployeeData(category) {
  try {
    const companyRef = db.collection("companies").doc(userCompany);
    const employeesData = [];

    const usersSnapshot = await companyRef.collection("users").get();

    const resultsPromises = usersSnapshot.docs.map(async (userDoc) => {
      const userData = userDoc.data();

      // Fetch all three assessment types
      const [digitalResults, softSkillsResults, aiResults] = await Promise.all([
          userDoc.ref.collection("assessmentResults")
              .orderBy("timestamp", "desc")
              .limit(1)
              .get(),
          userDoc.ref.collection("softSkillsAssessmentResults")
              .orderBy("timestamp", "desc")
              .limit(1)
              .get(),
          userDoc.ref.collection("assessmentResults_ai")
              .orderBy("metadata.timestamp", "desc")
              .limit(1)
              .get()
      ]);

      const digitalResult = !digitalResults.empty ? digitalResults.docs[0].data() : null;
      const softSkillsResult = !softSkillsResults.empty ? softSkillsResults.docs[0].data() : null;
      const aiResult = !aiResults.empty ? aiResults.docs[0].data() : null;

      // Get sections from all three assessments
      const digitalSection = digitalResult?.section?.toLowerCase();
      const softSkillsSection = softSkillsResult?.section?.toLowerCase();
      const aiSection = aiResult?.metadata?.learningPath?.toLowerCase() ||
                       (userData.skillsAnalysis_ai ?
                         (userData.skillsAnalysis_ai.toLowerCase().includes('advanced') ? 'advanced' :
                          userData.skillsAnalysis_ai.toLowerCase().includes('intermediate') ? 'intermediate' : 'essentials')
                         : null);

      // Include user if ANY assessment matches the current category
      if (digitalSection === category.toLowerCase() ||
          softSkillsSection === category.toLowerCase() ||
          aiSection === category.toLowerCase()) {
          employeesData.push({
              name: `${userData.firstName} ${userData.lastName}`,
              role: userData.userRole || "Employee",
              enrollmentStatus: userData.enrollmentStatus || "not enrolled",
              assessments: {
                  digital: digitalResult ? {
                      section: digitalSection,
                      timestamp: digitalResult.timestamp
                  } : null,
                  softSkills: softSkillsResult ? {
                      section: softSkillsSection,
                      timestamp: softSkillsResult.timestamp
                  } : null,
                  ai: aiResult ? {
                      section: aiSection,
                      timestamp: aiResult.metadata?.timestamp
                  } : null
              }
          });
      }
    });

    await Promise.all(resultsPromises);
    return employeesData;

  } catch (error) {
    console.error("Error fetching employee data:", error);
    return [];
  }
}

//------------------------------------------------------
// INITIALIZE DETAIL PAGE
//------------------------------------------------------
async function initializeDetail(category) {
  try {
    sessionStorage.setItem("currentCategory", category);

    // Track detail page access
    trackMilestone('detail_page_viewed', {
      category: category,
      userCompany: userCompany
    });

    // Ensure we use the right company name when in demo mode
    if (window.isDemoMode) {
      userCompany = 'Barefoot eLearning';
      console.log('Detail page using demo company:', userCompany);
    } else if (window.currentDetailCompany) {
      userCompany = window.currentDetailCompany;
      console.log('Detail page using company from window:', userCompany);
    }

    console.log('Detail page initialized with company:', userCompany);

    // Show skeleton loaders
    showSkeletonLoaders();

    // Fetch data in parallel
    const [enrollmentStatus, learningPathData, employees] = await Promise.all([
      fetchEnrollmentStatus(category),
      fetchLearningPathData(category),
      fetchEmployeeData(category),
    ]);

    // Hide skeleton loaders
    hideSkeletonLoaders();

    // Update UI with fetched data
    updateEnrollmentStatus(enrollmentStatus);
    populateLearningPath(learningPathData);
    populateEmployeeList(employees);

    handleScrollableSection();
    addEventListeners();
  } catch (error) {
    console.error("Error initializing detail:", error);
    // Handle the error appropriately (e.g., show an error message to the user)
  }
}

//------------------------------------------------------
// FETCH LEARNING PATH DATA
//------------------------------------------------------
async function fetchLearningPathData(category) {
  // Fetch all three skill types data in parallel
  const [digitalResponse, softSkillsResponse, aiResponse] = await Promise.all([
    fetch("learning-path-data.json"),
    fetch("learning-path-data_softskills.json"),
    fetch("learning-path-data_ai.json")
  ]);

  if (!digitalResponse.ok) {
    throw new Error(`HTTP error fetching digital skills data! status: ${digitalResponse.status}`);
  }
  if (!softSkillsResponse.ok) {
    throw new Error(`HTTP error fetching soft skills data! status: ${softSkillsResponse.status}`);
  }
  if (!aiResponse.ok) {
    throw new Error(`HTTP error fetching AI skills data! status: ${aiResponse.status}`);
  }

  const digitalData = await digitalResponse.json();
  const softSkillsData = await softSkillsResponse.json();
  const aiData = await aiResponse.json();

  // Get the appropriate category data from each source
  const digitalPathData = digitalData[category] || digitalData.essentials;
  const softSkillsPathData = softSkillsData[category] || softSkillsData.essentials;
  const aiPathData = aiData[category] || aiData.essentials;

  // Mark each course with its skill type
  digitalPathData.courseCategories.forEach(category => {
    category.courses.forEach(course => {
      course.skillType = 'digital';
    });
  });

  softSkillsPathData.courseCategories.forEach(category => {
    category.courses.forEach(course => {
      course.skillType = 'soft';
    });
  });

  aiPathData.courseCategories.forEach(category => {
    category.courses.forEach(course => {
      course.skillType = 'ai';
    });
  });

  // Create a combined data structure
  const combinedData = {
    title: digitalPathData.title, // Use digital skills title as the main title
    description: digitalPathData.description, // Use digital skills description as the main description
    softSkillsTitle: softSkillsPathData.title, // Store soft skills title separately
    softSkillsDescription: softSkillsPathData.description, // Store soft skills description separately
    aiTitle: aiPathData.title, // Store AI skills title separately
    aiDescription: aiPathData.description, // Store AI skills description separately
    courseCategories: [...digitalPathData.courseCategories, ...softSkillsPathData.courseCategories, ...aiPathData.courseCategories]
  };

  return combinedData;
}

//------------------------------------------------------
// SHOW SKELETON LOADERS
//------------------------------------------------------
function showSkeletonLoaders() {
  // Employee list skeleton loaders
  const employeeList = document.getElementById("employee-list");
  employeeList.innerHTML = "";
  for (let i = 0; i < 5; i++) {
    const skeletonLoader = document.createElement("div");
    skeletonLoader.classList.add(
      "skeleton-loader",
      "card",
      "bg-white",
      "p-3",
      "rounded-md",
      "shadow-sm",
      "flex",
      "items-center",
      "mb-2",
      "animate-pulse"
    );
    skeletonLoader.innerHTML = `
      <div class="w-8 h-8 bg-gray-300 rounded-full mr-3"></div>
      <div class="flex-1">
        <div class="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
        <div class="h-3 bg-gray-300 rounded w-1/2"></div>
      </div>
    `;
    employeeList.appendChild(skeletonLoader);
  }

  // Course list skeleton loaders
  const courseList = document.getElementById("course-list");
  courseList.innerHTML = "";
  for (let i = 0; i < 3; i++) {
    const skeletonLoader = document.createElement("div");
    skeletonLoader.classList.add(
      "skeleton-loader",
      "card",
      "bg-white",
      "p-5",
      "rounded-lg",
      "shadow-sm",
      "mb-4",
      "animate-pulse"
    );
    skeletonLoader.innerHTML = `
      <div class="h-6 bg-gray-300 rounded w-1/2 mb-4"></div>
      <div class="space-y-2">
        <div class="h-4 bg-gray-300 rounded w-full"></div>
        <div class="h-4 bg-gray-300 rounded w-5/6"></div>
        <div class="h-4 bg-gray-300 rounded w-3/4"></div>
      </div>
    `;
    courseList.appendChild(skeletonLoader);
  }
}

//------------------------------------------------------
// HIDE SKELETON LOADERS
//------------------------------------------------------
function hideSkeletonLoaders() {
  const employeeList = document.getElementById("employee-list");
  employeeList.innerHTML = "";

  const courseList = document.getElementById("course-list");
  courseList.innerHTML = "";
}

//------------------------------------------------------
// UPDATE ENROLLMENT STATUS
//------------------------------------------------------
function updateEnrollmentStatus({ totalEmployees, notEnrolledEmployees, processingEmployees }) {
  const enrollmentStatusDiv = document.querySelector(".card.p-6");
  const enrollmentStatusText = enrollmentStatusDiv.querySelector("p");
  const enrollButton = document.getElementById("book-now-btn");

  // Get the duplicate section at the bottom right
  const duplicateStatusDiv = document.querySelector(".lg\\:col-span-2 .card.p-6");
  const duplicateStatusText = duplicateStatusDiv.querySelector("p");
  const duplicateEnrollButton = document.getElementById("duplicate-book-now-btn");

  if (totalEmployees === 0) {
    const statusMessage = `You have 0 employees on this pathway`;

    // Update both instances
    enrollmentStatusText.innerHTML = statusMessage;
    duplicateStatusText.innerHTML = statusMessage;

    enrollmentStatusDiv.classList.add("opacity-50");
    duplicateStatusDiv.classList.add("opacity-50");

    enrollButton.disabled = true;
    duplicateEnrollButton.disabled = true;

    enrollButton.classList.add("opacity-50", "cursor-not-allowed");
    duplicateEnrollButton.classList.add("opacity-50", "cursor-not-allowed");
  } else if (notEnrolledEmployees === 0 && processingEmployees === 0) {
    const statusMessage = `All employees successfully enrolled <svg class="inline-block w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>`;

    // Update both instances
    enrollmentStatusText.innerHTML = statusMessage;
    duplicateStatusText.innerHTML = statusMessage;

    enrollButton.disabled = true;
    duplicateEnrollButton.disabled = true;

    enrollButton.classList.add("opacity-50", "cursor-not-allowed");
    duplicateEnrollButton.classList.add("opacity-50", "cursor-not-allowed");
  } else if (processingEmployees === totalEmployees) {
    const statusMessage = `All <span class="text-yellow-600 font-bold text-lg">${totalEmployees}</span> employees are currently being processed`;

    // Update both instances
    enrollmentStatusText.innerHTML = statusMessage;
    duplicateStatusText.innerHTML = statusMessage;

    enrollButton.disabled = true;
    duplicateEnrollButton.disabled = true;

    enrollButton.classList.add("opacity-50", "cursor-not-allowed");
    duplicateEnrollButton.classList.add("opacity-50", "cursor-not-allowed");
  } else {
    const totalNotEnrolled = notEnrolledEmployees + processingEmployees;

    // Check for AI recommendations
    checkAIRecommendations(totalNotEnrolled).then((hasRecommendations) => {
      let statusText = `You currently have <span style="color: #68C692" class="font-bold text-lg">${totalNotEnrolled}</span> employee${
        totalNotEnrolled > 1 ? "s" : ""
      } on this pathway not enrolled.`;

      if (processingEmployees > 0) {
        statusText += ` (including <span class="text-yellow-600 font-bold text-md">${processingEmployees}</span> processing).`;
      }

      if (hasRecommendations) {
        statusText += ` <span style="color: #68C692" class="cursor-pointer hover:underline" id="viewRecommendations">View course recommendations.</span>`;
      }

      // Update both instances
      enrollmentStatusText.innerHTML = statusText;
      duplicateStatusText.innerHTML = statusText;

      // Add click event listener for recommendations to both recommendation links
      const recommendationsLinks = document.querySelectorAll("[id='viewRecommendations']");
      recommendationsLinks.forEach(link => {
        link.addEventListener("click", showRecommendationsOverlay);
      });
    });

    enrollButton.disabled = false;
    duplicateEnrollButton.disabled = false;

    enrollButton.classList.remove("opacity-50", "cursor-not-allowed");
    duplicateEnrollButton.classList.remove("opacity-50", "cursor-not-allowed");
  }
}

//------------------------------------------------------
// POPULATE THE LEARNING PATH
//------------------------------------------------------
function populateLearningPath(data) {
  // Populate the learning pathway title and description
  document.getElementById("learning-path").textContent = data.title;
  document.getElementById("description").textContent = data.description;

  // Populate the course list with categories
  const courseList = document.getElementById("course-list");
  courseList.classList.add("fade-in");
  courseList.innerHTML = ""; // Clear existing courses

  // Store all courses for filtering
  window.allCourses = [];

  data.courseCategories.forEach((categoryData) => {
    const categorySection = document.createElement("div");
    categorySection.classList.add("course-category", "mb-8", "fade-in");
    categorySection.dataset.category = categoryData.category;

    const categoryTitle = document.createElement("h3");
    categoryTitle.classList.add(
      "category-title",
      "text-xl",
      "font-semibold",
      "mb-4"
    );
    categoryTitle.textContent = categoryData.category;
    categorySection.appendChild(categoryTitle);

    categoryData.courses.forEach((course) => {
      // Store course for filtering
      window.allCourses.push({
        element: null, // Will be set below
        title: course.title,
        description: course.description,
        level: course.level,
        category: categoryData.category,
        skillType: course.skillType
      });

      const courseCard = document.createElement("div");
      courseCard.classList.add(
        "card",
        "bg-white",
        "p-5",
        "rounded-lg",
        "shadow-sm",
        "mb-4",
        "fade-in",
        `skill-${course.skillType}`
      );
      courseCard.dataset.skillType = course.skillType;
      courseCard.dataset.title = course.title.toLowerCase();
      courseCard.dataset.description = course.description.toLowerCase();
      courseCard.dataset.level = course.level.toLowerCase();
      courseCard.dataset.category = categoryData.category.toLowerCase();

      // Determine badge color and icon based on skill type
      let skillBadgeClass = '';
      let skillBadgeLabel = '';
      let skillBadgeIcon = '';
      if (course.skillType === 'digital') {
        skillBadgeClass = 'bg-green-100 text-green-600';
        skillBadgeLabel = 'Digital Skills';
        skillBadgeIcon = `
          <svg class="w-3.5 h-3.5 mr-1 inline-block align-[-1px]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>`;
      } else if (course.skillType === 'soft') {
        skillBadgeClass = 'bg-purple-100 text-purple-600';
        skillBadgeLabel = 'Soft Skills';
        skillBadgeIcon = `
          <svg class="w-3.5 h-3.5 mr-1 inline-block align-[-1px]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>`;
      } else if (course.skillType === 'ai') {
        skillBadgeClass = 'bg-green-100 text-green-600';
        skillBadgeLabel = 'AI Skills';
        skillBadgeIcon = `
          <svg class="w-3.5 h-3.5 mr-1 inline-block align-[-1px]" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 2a6 6 0 00-6 6 6 6 0 003.429 5.385V15a2 2 0 002 2h1.142a2 2 0 002-2v-1.615A6 6 0 009 2z" />
          </svg>`;
      } else {
        // Fallback
        skillBadgeClass = 'bg-gray-100 text-gray-600';
        skillBadgeLabel = 'Skills';
      }

      courseCard.innerHTML = `
        <div class="flex justify-between items-start">
          <h4 class="text-lg font-semibold mb-2" style="color: #68C692">${course.title}</h4>
          <span class="${skillBadgeClass} px-2 py-1 rounded-full text-xs font-medium flex items-center">
            ${skillBadgeIcon}${skillBadgeLabel}
          </span>
        </div>
        <p class="text-sm text-gray-600 mb-3">${course.description}</p>
        <div class="flex justify-between items-center">
          <span class="inline-block bg-gray-100 px-2 py-1 rounded-full text-xs" style="color: #68C692">${course.level}</span>
        </div>
      `;

      // Store reference to DOM element for filtering
      window.allCourses[window.allCourses.length - 1].element = courseCard;

      categorySection.appendChild(courseCard);
    });

    courseList.appendChild(categorySection);
  });

  // Initialize search and filter functionality
  initializeSearchAndFilter();
}

//------------------------------------------------------
// POPULATE THE EMPLOYEE LIST
//------------------------------------------------------
function populateEmployeeList(employees) {
  const employeeList = document.getElementById("employee-list");
  employeeList.classList.add("fade-in");
  employeeList.innerHTML = ""; // Clear existing content

  const employeesTitle = document.getElementById("employees-on-this-pathway");
  if (employeesTitle) {
    employeesTitle.textContent =
      employees.length > 0
        ? `Employees on this pathway (${employees.length})`
        : "Employees on this pathway";
  }

  if (employees.length === 0) {
    const noEmployeesMessage = document.createElement("p");
    noEmployeesMessage.textContent =
      "No employees currently on this learning pathway. Encourage your workforce to keep taking the assessments!";
    noEmployeesMessage.classList.add("text-sm", "text-gray-500", "p-3");
    employeeList.appendChild(noEmployeesMessage);
  } else {
    employees.forEach((employee) => {
      const employeeCard = document.createElement("div");
      employeeCard.classList.add(
        "card",
        "bg-white",
        "p-3",
        "rounded-md",
        "shadow-sm",
        "flex",
        "items-center",
        "mb-2",
        "fade-in"
      );

      let statusColor, statusText;
      switch (employee.enrollmentStatus) {
        case "enrolled":
          statusColor = "text-green-600";
          statusText = "Enrolled";
          break;
        case "processing":
          statusColor = "text-yellow-600";
          statusText = "Processing";
          break;
        default:
          statusColor = "text-gray-500";
          statusText = "Not Enrolled";
      }

      // Create assessment type indicators with only icons
      const assessmentBadges = [];
      if (employee.assessments.digital) {
        assessmentBadges.push(`
          <span class="inline-flex items-center justify-center w-5 h-5
            text-green-600 hover:text-green-700 transition-colors duration-200 group relative"
            title="Digital Skills Assessment">
            <svg class="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span class="absolute bottom-full mb-1 hidden group-hover:block w-28 bg-gray-900 text-white text-[10px] px-2 py-1 rounded-md text-center -ml-14">
              Digital Skills
            </span>
          </span>`);
      }
      if (employee.assessments.softSkills) {
        assessmentBadges.push(`
          <span class="inline-flex items-center justify-center w-5 h-5
            text-purple-600 hover:text-purple-700 transition-colors duration-200 group relative"
            title="Soft Skills Assessment">
            <svg class="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span class="absolute bottom-full mb-1 hidden group-hover:block w-24 bg-gray-900 text-white text-[10px] px-2 py-1 rounded-md text-center -ml-12">
              Soft Skills
            </span>
          </span>`);
      }
  if (employee.assessments.ai) {
    assessmentBadges.push(`
      <span class="inline-flex items-center justify-center w-5 h-5
        text-green-600 hover:text-green-700 transition-colors duration-200 group relative"
        title="AI Skills Assessment">
        <svg class="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3a7 7 0 00-7 7 7 7 0 004 6.326V18a2 2 0 002 2h2a2 2 0 002-2v-1.674A7.001 7.001 0 0018 10a7 7 0 00-7-7z" />
        </svg>
        <span class="absolute bottom-full mb-1 hidden group-hover:block w-16 bg-gray-900 text-white text-[10px] px-2 py-1 rounded-md text-center -ml-8">
          AI Skills
        </span>
      </span>`);
  }

      employeeCard.innerHTML = `
        <img src="people.png" alt="${employee.name}" class="w-8 h-8 mr-3 rounded-full" />
        <div class="flex-grow">
          <h3 class="text-sm font-medium">${employee.name}</h3>
          <p class="text-xs text-gray-500">${employee.role}</p>
          <div class="flex gap-1.5 mt-1">
            ${assessmentBadges.join('')}
          </div>
        </div>
        <span class="text-xs font-medium ${statusColor}">${statusText}</span>
      `;
      employeeList.appendChild(employeeCard);
    });
  }
}

//------------------------------------------------------
// HANDLE SCROLLABLE SECTION
//------------------------------------------------------
function handleScrollableSection() {
  const employeeList = document.getElementById("employee-list");
  const scrollIndicator = document.getElementById("scroll-indicator");

  function updateScrollIndicator() {
    if (employeeList.scrollHeight > employeeList.clientHeight) {
      scrollIndicator.classList.remove("hidden");
    } else {
      scrollIndicator.classList.add("hidden");
    }

    // Hide indicator when scrolled to bottom
    if (employeeList.scrollHeight - employeeList.scrollTop <= employeeList.clientHeight + 1) {
      scrollIndicator.classList.add("hidden");
    }
  }

  // Initial check
  updateScrollIndicator();

  // Scroll events
  employeeList.addEventListener("scroll", updateScrollIndicator);

  // Click event
  scrollIndicator.addEventListener("click", () => {
    employeeList.scrollTo({
      top: employeeList.scrollHeight,
      behavior: "smooth",
    });
  });

  // Resize event
  window.addEventListener("resize", updateScrollIndicator);
}

//------------------------------------------------------
// FETCH ENROLLMENT STATUS
//------------------------------------------------------
async function fetchEnrollmentStatus(category) {
  try {
    const companyRef = db.collection("companies").doc(userCompany);
    const usersSnapshot = await companyRef.collection("users").get();

    let totalEmployees = 0;
    let notEnrolledEmployees = 0;
    let processingEmployees = 0;

    const enrollmentPromises = usersSnapshot.docs.map(async (userDoc) => {
      const userData = userDoc.data();

      // Fetch all three assessment types
      const [digitalResults, softSkillsResults, aiResults] = await Promise.all([
          userDoc.ref.collection("assessmentResults")
              .orderBy("timestamp", "desc")
              .limit(1)
              .get(),
          userDoc.ref.collection("softSkillsAssessmentResults")
              .orderBy("timestamp", "desc")
              .limit(1)
              .get(),
          userDoc.ref.collection("assessmentResults_ai")
              .orderBy("metadata.timestamp", "desc")
              .limit(1)
              .get()
      ]);

      const digitalResult = !digitalResults.empty ? digitalResults.docs[0].data() : null;
      const softSkillsResult = !softSkillsResults.empty ? softSkillsResults.docs[0].data() : null;
      const aiResult = !aiResults.empty ? aiResults.docs[0].data() : null;

      const digitalSection = digitalResult?.section?.toLowerCase();
      const softSkillsSection = softSkillsResult?.section?.toLowerCase();
      const aiSection = aiResult?.metadata?.learningPath?.toLowerCase() ||
                       (userData.skillsAnalysis_ai ?
                         (userData.skillsAnalysis_ai.toLowerCase().includes('advanced') ? 'advanced' :
                          userData.skillsAnalysis_ai.toLowerCase().includes('intermediate') ? 'intermediate' : 'essentials')
                         : null);

      if (digitalSection === category.toLowerCase() ||
          softSkillsSection === category.toLowerCase() ||
          aiSection === category.toLowerCase()) {
          totalEmployees++;
          if (userData.enrollmentStatus === "processing") {
              processingEmployees++;
          } else if (userData.enrollmentStatus !== "enrolled") {
              notEnrolledEmployees++;
          }
      }
    });

    await Promise.all(enrollmentPromises);
    return { totalEmployees, notEnrolledEmployees, processingEmployees };

  } catch (error) {
    console.error("Error fetching enrollment status:", error);
    return { totalEmployees: 0, notEnrolledEmployees: 0, processingEmployees: 0 };
  }
}



//------------------------------------------------------
// INITIALIZE SEARCH AND FILTER FUNCTIONALITY
//------------------------------------------------------
function initializeSearchAndFilter() {
  const searchInput = document.getElementById('course-search');
  const filterAllBtn = document.getElementById('filter-all');
  const filterDigitalBtn = document.getElementById('filter-digital');
  const filterSoftBtn = document.getElementById('filter-soft');
  const filterAiBtn = document.getElementById('filter-ai');

  // Set initial active state
  let activeFilter = 'all';

  // Counts helper
  function computeCounts(filterText = '') {
    const t = filterText.toLowerCase();
    const matches = (c) => {
      if (!t) return true;
      return (
        c.title.toLowerCase().includes(t) ||
        (c.description || '').toLowerCase().includes(t) ||
        (c.level || '').toLowerCase().includes(t) ||
        (c.category || '').toLowerCase().includes(t)
      );
    };
    const all = window.allCourses.filter((c) => matches(c));
    return {
      all: all.length,
      digital: all.filter((c) => c.skillType === 'digital').length,
      soft: all.filter((c) => c.skillType === 'soft').length,
      ai: all.filter((c) => c.skillType === 'ai').length,
    };
  }

  function updateCountBadges(filterText = '') {
    const counts = computeCounts(filterText);
    filterAllBtn.textContent = `All (${counts.all})`;
    filterDigitalBtn.textContent = `Digital Skills (${counts.digital})`;
    filterSoftBtn.textContent = `Soft Skills (${counts.soft})`;
    filterAiBtn.textContent = `AI Skills (${counts.ai})`;
  }

  // Helper function to update filter button states
  function updateFilterButtons() {
    // Reset all buttons
    filterAllBtn.className = 'filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200';
    filterDigitalBtn.className = 'filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200';
    filterSoftBtn.className = 'filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200';
    filterAiBtn.className = 'filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200';

    // Set active button
    if (activeFilter === 'all') {
      filterAllBtn.className = 'filter-button active px-3 py-1.5 text-xs font-medium rounded-md bg-primary text-white';
    } else if (activeFilter === 'digital') {
      filterDigitalBtn.className = 'filter-button active px-3 py-1.5 text-xs font-medium rounded-md bg-green-600 text-white';
    } else if (activeFilter === 'soft') {
      filterSoftBtn.className = 'filter-button active px-3 py-1.5 text-xs font-medium rounded-md bg-purple-600 text-white';
    } else if (activeFilter === 'ai') {
      filterAiBtn.className = 'filter-button active px-3 py-1.5 text-xs font-medium rounded-md bg-green-600 text-white';
    }
  }

  // Apply filters based on search text and active filter
  function applyFilters() {
    const searchText = searchInput.value.toLowerCase().trim();
  // Update dynamic counts based on current search
  updateCountBadges(searchText);

    // Get all course categories to check if they should be hidden
    const categories = document.querySelectorAll('.course-category');
    const categoryVisibility = {};

    // Process each course
    window.allCourses.forEach(course => {
      const element = course.element;
      let visible = true;

      // Apply skill type filter
      if (activeFilter !== 'all' && course.skillType !== activeFilter) {
        visible = false;
      }

      // Apply search filter if there's search text
      if (searchText && visible) {
        const matchesTitle = course.title.toLowerCase().includes(searchText);
        const matchesDescription = course.description.toLowerCase().includes(searchText);
        const matchesLevel = course.level.toLowerCase().includes(searchText);
        const matchesCategory = course.category.toLowerCase().includes(searchText);

        visible = matchesTitle || matchesDescription || matchesLevel || matchesCategory;
      }

      // Update visibility
      if (visible) {
        element.classList.remove('hidden');
        // Track that this category has visible courses
        categoryVisibility[course.category] = true;
      } else {
        element.classList.add('hidden');
      }
    });

    // Hide categories with no visible courses
    categories.forEach(category => {
      const categoryName = category.dataset.category;
      if (categoryVisibility[categoryName]) {
        category.classList.remove('hidden');
      } else {
        category.classList.add('hidden');
      }
    });

    // Show a message if no courses match
    const courseList = document.getElementById('course-list');
    const noResultsMessage = document.getElementById('no-results-message');

    // Check if any courses are visible
    const hasVisibleCourses = window.allCourses.some(course =>
      !course.element.classList.contains('hidden')
    );

    // Remove existing message if it exists
    if (noResultsMessage) {
      noResultsMessage.remove();
    }

    // Add message if no courses are visible
    if (!hasVisibleCourses) {
      const message = document.createElement('div');
      message.id = 'no-results-message';
      message.className = 'text-center py-8 text-gray-500';
      message.innerHTML = `
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <p class="mt-4 text-sm">No courses found matching your criteria</p>
      `;
      courseList.appendChild(message);
    }
  }

  // Add event listeners
  searchInput.addEventListener('input', applyFilters);

  filterAllBtn.addEventListener('click', () => {
    activeFilter = 'all';
    updateFilterButtons();
    applyFilters();
  });

  filterDigitalBtn.addEventListener('click', () => {
    activeFilter = 'digital';
    updateFilterButtons();
    applyFilters();
  });

  filterSoftBtn.addEventListener('click', () => {
    activeFilter = 'soft';
    updateFilterButtons();
    applyFilters();
  });

  filterAiBtn.addEventListener('click', () => {
    activeFilter = 'ai';
    updateFilterButtons();
    applyFilters();
  });

  // Initialize filter buttons
  updateFilterButtons();
  updateCountBadges();
}

//------------------------------------------------------
// ADD EVENT LISTENERS
//------------------------------------------------------
function addEventListeners() {
  const getStartedBtn = document.getElementById("get-started-btn");
  if (getStartedBtn) {
    getStartedBtn.addEventListener("click", () => {
      openSharedCalendar();
    });
  }

  const bookNowBtn = document.getElementById("book-now-btn");
  if (bookNowBtn) {
    bookNowBtn.addEventListener("click", () => {
      const currentCategory = sessionStorage.getItem("currentCategory");
      console.log("Current category before loading enrollment page:", currentCategory);
      
      // Track book now action
      trackMilestone('enrollment_initiated', {
        category: currentCategory,
        source: 'book-now-button'
      });
      
      loadEnrollmentPage(getMainContent(), currentCategory);
    });
  }

  // Add the same event listener to the duplicate button
  const duplicateBookNowBtn = document.getElementById("duplicate-book-now-btn");
  if (duplicateBookNowBtn) {
    duplicateBookNowBtn.addEventListener("click", () => {
      const currentCategory = sessionStorage.getItem("currentCategory");
      console.log("Current category before loading enrollment page:", currentCategory);
      
      // Track book now action
      trackMilestone('enrollment_initiated', {
        category: currentCategory,
        source: 'duplicate-book-now-button'
      });
      
      loadEnrollmentPage(getMainContent(), currentCategory);
    });
  }
}

//------------------------------------------------------
// LOAD ENROLLMENT PAGE
//------------------------------------------------------
function loadEnrollmentPage(mainContent, category, params = {}) {
  console.log("Loading enrollment page...", { category, userCompany, params });
  showLoadingOverlay();

  fetch("enrollment.html")
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.text();
    })
    .then((data) => {
      mainContent.innerHTML = data;
      currentContent = "enrollment";
      pushToNavigationState("enrollment", category, params);

      // Remove any existing enrollment.js script
      const existingScript = document.querySelector('script[src="enrollment.js"]');
      if (existingScript) {
        existingScript.remove();
      }

      const enrollmentScript = document.createElement("script");
      enrollmentScript.src = "enrollment.js";
      enrollmentScript.onload = () => {
        if (typeof initializeEnrollment === "function") {
          console.log("Calling initializeEnrollment with:", { category, userCompany, params });
          initializeEnrollment(category, userCompany, params);
        } else {
          console.error("initializeEnrollment function not found");
        }
        hideLoadingOverlay();
      };
      enrollmentScript.onerror = (error) => {
        console.error("Error loading enrollment.js:", error);
        hideLoadingOverlay();
      };
      document.body.appendChild(enrollmentScript);
    })
    .catch((error) => {
      console.error("Error loading enrollment.html:", error);
      hideLoadingOverlay();
    });
}

//------------------------------------------------------
// FETCH LEARNERS WITH BOTH CORE & OTHER RECOMMENDATIONS
//------------------------------------------------------
async function fetchLearnersWithRecommendations(category) {
  try {
    const companyRef = db.collection("companies").doc(userCompany);
    const usersSnapshot = await companyRef.collection("users").get();
    const learnersWithRecommendations = [];

    for (const doc of usersSnapshot.docs) {
      const userData = doc.data();

      // Check if user belongs to this category/pathway by checking all assessment types
      const [digitalResultsSnapshot, softSkillsResultsSnapshot, aiResultsSnapshot] = await Promise.all([
        doc.ref.collection("assessmentResults")
          .orderBy("timestamp", "desc")
          .limit(1)
          .get(),
        doc.ref.collection("softSkillsAssessmentResults")
          .orderBy("timestamp", "desc")
          .limit(1)
          .get(),
        doc.ref.collection("assessmentResults_ai")
          .orderBy("metadata.timestamp", "desc")
          .limit(1)
          .get()
      ]);

      const digitalResult = !digitalResultsSnapshot.empty ? digitalResultsSnapshot.docs[0].data() : null;
      const softSkillsResult = !softSkillsResultsSnapshot.empty ? softSkillsResultsSnapshot.docs[0].data() : null;
      const aiResult = !aiResultsSnapshot.empty ? aiResultsSnapshot.docs[0].data() : null;

      const digitalSection = digitalResult?.section?.toLowerCase();
      const softSkillsSection = softSkillsResult?.section?.toLowerCase();
      const aiSection = aiResult?.metadata?.learningPath?.toLowerCase() ||
                       (userData.skillsAnalysis_ai ?
                         (userData.skillsAnalysis_ai.toLowerCase().includes('advanced') ? 'advanced' :
                          userData.skillsAnalysis_ai.toLowerCase().includes('intermediate') ? 'intermediate' : 'essentials')
                         : null);

      // Skip if none of the assessments match the current category
      if (digitalSection !== category.toLowerCase() &&
          softSkillsSection !== category.toLowerCase() &&
          aiSection !== category.toLowerCase()) continue;

      // Skip if already enrolled
      if (userData.enrollmentStatus === "enrolled") continue;

      // Fetch digital skills recommendations (from user document)
      const digitalCoreRecs = Array.isArray(userData.courseRecommendations)
        ? userData.courseRecommendations
        : [];

      const digitalOtherRecs = Array.isArray(userData.otherPathRecommendations)
        ? userData.otherPathRecommendations.map(rec => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason,
            learningPath: rec.learningPath,
          }))
        : [];

      // Fetch soft skills recommendations directly from assessments
      let softSkillsRecs = [];
      let softSkillsOtherRecs = [];

      // Check for last soft skills assessment ID
      if (userData.lastSoftSkillsAssessmentId) {
        const softSkillsDoc = await doc.ref
          .collection("softSkillsAssessmentResults")
          .doc(userData.lastSoftSkillsAssessmentId)
          .get();

        if (softSkillsDoc.exists) {
          const softSkillsData = softSkillsDoc.data();

          // Get soft skills core recommendations
          softSkillsRecs = Array.isArray(softSkillsData.courseRecommendations)
            ? softSkillsData.courseRecommendations.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                assessmentType: 'soft skills'
              }))
            : [];

          // Get soft skills other path recommendations
          softSkillsOtherRecs = Array.isArray(softSkillsData.otherPathRecommendations)
            ? softSkillsData.otherPathRecommendations.map(rec => ({
                course: rec.courseName || rec.course,
                reason: rec.justification || rec.reason,
                learningPath: rec.learningPath,
                assessmentType: 'soft skills'
              }))
            : [];
        }
      }

      // Fetch AI skills recommendations directly from assessments
      let aiRecs = [];
      let aiOtherRecs = [];

      // Check for last AI skills assessment ID
      if (userData.lastAssessmentId_ai) {
        const aiDoc = await doc.ref
          .collection("assessmentResults_ai")
          .doc(userData.lastAssessmentId_ai)
          .get();

        if (aiDoc.exists) {
          const aiData = aiDoc.data();

          // Get AI skills core recommendations
          aiRecs = Array.isArray(aiData.courseRecommendations)
            ? aiData.courseRecommendations.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                assessmentType: 'AI skills'
              }))
            : [];

          // Get AI skills other path recommendations
          aiOtherRecs = Array.isArray(aiData.otherPathRecommendations)
            ? aiData.otherPathRecommendations.map(rec => ({
                course: rec.courseName || rec.course,
                reason: rec.justification || rec.reason,
                learningPath: rec.learningPath,
                assessmentType: 'AI skills'
              }))
            : [];
        }
      }

      // Add assessment type to digital skills recommendations for display purposes
      const taggedDigitalCoreRecs = digitalCoreRecs.map(rec => ({
        ...rec,
        assessmentType: 'digital skills'
      }));

      const taggedDigitalOtherRecs = digitalOtherRecs.map(rec => ({
        ...rec,
        assessmentType: 'digital skills'
      }));

      // Combine all recommendations from all three assessment types
      const allCoreRecs = [...taggedDigitalCoreRecs, ...softSkillsRecs, ...aiRecs];
      const allOtherRecs = [...taggedDigitalOtherRecs, ...softSkillsOtherRecs, ...aiOtherRecs];

      // Only add learner if they have any recommendations (from any assessment type)
      if (allCoreRecs.length > 0 || allOtherRecs.length > 0) {
        learnersWithRecommendations.push({
          name: `${userData.firstName} ${userData.lastName}`,
          id: doc.id,
          corePathRecommendations: allCoreRecs,
          otherPathRecommendations: allOtherRecs,
          role: userData.userRole || "Employee",
        });
      }
    }

    return learnersWithRecommendations;
  } catch (error) {
    console.error("Error fetching learners with recommendations:", error);
    return [];
  }
}

//------------------------------------------------------
// SHOW COURSE RECOMMENDATIONS OVERLAY (CORE + OTHER)
//------------------------------------------------------
async function showRecommendationsOverlay() {
  // Show loading overlay immediately
  showLoadingOverlay();

  try {
    const currentCategory = sessionStorage.getItem("currentCategory");
    const overlay = document.createElement("div");
    overlay.className =
      "fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4";

    // Fetch learners (both sets of recs)
    const learnersWithRecommendations = await fetchLearnersWithRecommendations(currentCategory);

    // Summaries for the header
    const totalRecommendations = learnersWithRecommendations.reduce((total, learner) => {
      return (
        total +
        learner.corePathRecommendations.length +
        learner.otherPathRecommendations.length
      );
    }, 0);

    // Gather unique roles
    const rolesSet = new Set(learnersWithRecommendations.map((l) => l.role));
    const roles = Array.from(rolesSet);

    // Build overlay
    overlay.innerHTML = `
      <div class="w-full max-w-5xl bg-white rounded-lg shadow-xl relative max-h-[80vh] flex flex-col">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b">
          <div class="flex items-center gap-2">
            <img src="sparkling.png" alt="AI Recommendations" class="w-5 h-5" />
            <h2 class="text-xl font-semibold" style="color: #68C692">Course Recommendations</h2>
          </div>
          <button class="text-gray-500 hover:text-gray-700 transition-colors" id="closeOverlay">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <!-- Summary Section with Search -->
        <div style="background-color: #E5F2E5" class="p-4">
          <div class="flex flex-col gap-4">
            <div class="flex items-center gap-3" style="color: #68C692">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <div>
                <p style="color: #68C692">
                  <span class="font-semibold">${learnersWithRecommendations.length}</span>
                  ${currentCategory} learner${
      learnersWithRecommendations.length !== 1 ? "s" : ""
    } with
                  <span class="font-semibold">${totalRecommendations}</span> total recommendations
                </p>
              </div>
            </div>

            <!-- Search Bar -->
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <input
                type="text"
                id="searchInput"
                class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:border-transparent text-sm"
                style="focus:ring-color: #68C692"
                placeholder="Search by employee name..."
              />
            </div>

            <!-- Role Tabs -->
            <div class="mt-4">
              <div id="roleTabs" class="flex flex-wrap gap-2"></div>
            </div>
          </div>
        </div>

        <!-- Recommendations Content -->
        <div class="flex-grow overflow-auto p-4" style="max-height: 50vh;">
          <div id="recommendationsGrid" class="recommendations-grid">
            ${generateRecommendationsHTML(learnersWithRecommendations)}
          </div>
        </div>

        <!-- Action Footer -->
        <div class="border-t p-4 bg-gray-50">
          <div class="flex justify-end gap-3">
            <button
              class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors text-sm"
              id="cancelOverlay"
            >
              Cancel
            </button>
            <button
              class="px-4 py-2 text-white rounded-md transition-colors text-sm"
              style="background-color: #68C692; hover:background-color: #8DCE8C"
              id="exportToPdfButton"
            >
              Export to PDF
            </button>
            <button
              class="px-4 py-2 text-white rounded-md transition-colors text-sm"
              style="background-color: #68C692; hover:background-color: #8DCE8C"
              id="enrollButton"
            >
              Proceed to Enrollment
            </button>
          </div>
        </div>
      </div>
    `;

    // Append overlay to body
    document.body.appendChild(overlay);

    // Build role tabs (All + each unique role)
    const roleTabsContainer = document.getElementById("roleTabs");
    const allRoles = ["All", ...roles];
    let selectedRole = null;

    allRoles.forEach((role) => {
      const tabButton = document.createElement("button");
      tabButton.className = "px-3 py-1 rounded-full border text-sm focus:outline-none";
      tabButton.textContent = role;

      if (role === "All") {
        tabButton.style.backgroundColor = "#68C692";
        tabButton.style.color = "white";
      } else {
        tabButton.classList.add("bg-white", "text-gray-700", "border-gray-300");
      }

      tabButton.addEventListener("click", () => {
        // Update selectedRole
        selectedRole = role === "All" ? null : role;

        // Update tab styles
        document.querySelectorAll("#roleTabs button").forEach((btn) => {
          btn.style.backgroundColor = "white";
          btn.style.color = "#374151"; // text-gray-700
        });
        tabButton.style.backgroundColor = "#68C692";
        tabButton.style.color = "white";

        // Filter recommendations
        const searchTerm = document.getElementById("searchInput").value.toLowerCase();
        filterRecommendations(learnersWithRecommendations, searchTerm, selectedRole);
      });

      roleTabsContainer.appendChild(tabButton);
    });

    // Add search functionality
    const searchInput = document.getElementById("searchInput");
    searchInput.addEventListener("input", (e) => {
      const searchTerm = e.target.value.toLowerCase();
      filterRecommendations(learnersWithRecommendations, searchTerm, selectedRole);
    });

    // Close overlay
    document.getElementById("closeOverlay").addEventListener("click", () => {
      overlay.remove();
    });
    document.getElementById("cancelOverlay").addEventListener("click", () => {
      overlay.remove();
    });

    // Export to PDF
    document.getElementById("exportToPdfButton").addEventListener("click", async () => {
      const searchTerm = searchInput.value.toLowerCase();
      await exportRecommendationsToPDF(
        learnersWithRecommendations,
        currentCategory,
        totalRecommendations,
        selectedRole,
        searchTerm
      );
    });

    // Proceed to Enrollment
    document.getElementById("enrollButton").addEventListener("click", () => {
      overlay.remove();
      loadEnrollmentPage(getMainContent(), currentCategory);
    });
  } catch (error) {
    console.error("Error fetching recommendations:", error);
  } finally {
    hideLoadingOverlay();
  }
}

//------------------------------------------------------
// FILTER RECOMMENDATIONS
//------------------------------------------------------
function filterRecommendations(learners, searchTerm, selectedRole) {
  const recommendationsGrid = document.getElementById("recommendationsGrid");

  // Filter learners
  const filteredLearners = learners.filter((learner) => {
    const matchesSearchTerm = learner.name.toLowerCase().includes(searchTerm);
    const matchesRole = !selectedRole || learner.role === selectedRole;
    return matchesSearchTerm && matchesRole;
  });

  // Update the grid with filtered results
  recommendationsGrid.innerHTML = generateRecommendationsHTML(filteredLearners);

  // Show "no results" if none match
  if (filteredLearners.length === 0) {
    recommendationsGrid.innerHTML = `
      <div class="text-center py-8 text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <p class="mt-4 text-sm">No learners found matching your criteria</p>
      </div>
    `;
  }
}

//------------------------------------------------------
// GENERATE RECOMMENDATIONS HTML (CORE + OTHER)
//------------------------------------------------------
function generateRecommendationsHTML(learners) {
  return learners
    .map((learner) => {
      // Learner Name
      const learnerNameHTML = `
        <div class="learner-name w-full mb-2">
          <div class="flex items-center gap-2 mb-2">
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
            <h3 class="text-base font-semibold" style="color: #68C692">
              ${learner.name} <span class="text-sm text-gray-500">(${learner.role})</span>
            </h3>
          </div>
        </div>
      `;

      // Core Learning Path Recommendations
      let coreSection = "";
      if (learner.corePathRecommendations.length > 0) {
        const coreCards = learner.corePathRecommendations
          .map(
            (rec) => `
              <div class="recommendation-card">
                <div style="background-color: #E5F2E5" class="rounded p-3 flex flex-col h-full">
                  <p class="font-medium text-sm" style="color: ${rec.assessmentType === 'soft skills' ? '#6B21A8' : '#68C692'}">
                    ${rec.course}
                  </p>
                  <p class="text-xs text-gray-600 mt-1 flex-grow">${rec.reason}</p>
                </div>
              </div>
            `
          )
          .join("");

        coreSection = `
          <div class="core-recommendations mb-3">
            <h4 class="text-sm font-semibold mb-2">Core Learning Path Recommendations</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
              ${coreCards}
            </div>
          </div>
        `;
      }

      // Other Path Recommendations
      let otherSection = "";
      if (learner.otherPathRecommendations.length > 0) {
        const otherCards = learner.otherPathRecommendations
          .map(
            (rec) => `
              <div class="recommendation-card">
                <div style="background-color: #CEE8CD" class="rounded p-3 flex flex-col h-full">
                  <p class="font-medium text-sm" style="color: ${rec.assessmentType === 'soft skills' ? '#6B21A8' : '#68C692'}">
                    ${rec.course || rec.courseName}
                    <span class="text-xs text-gray-500">(${rec.learningPath})</span>
                  </p>
                  <p class="text-xs text-gray-600 mt-1 flex-grow">
                    ${rec.reason || rec.justification}
                  </p>
                </div>
              </div>
            `
          )
          .join("");

        otherSection = `
          <div class="other-recommendations mb-3">
            <h4 class="text-sm font-semibold mb-2">Other Path Recommendations</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
              ${otherCards}
            </div>
          </div>
        `;
      }

      return learnerNameHTML + coreSection + otherSection;
    })
    .join("");
}

//------------------------------------------------------
// EXPORT RECOMMENDATIONS TO PDF
//------------------------------------------------------
async function exportRecommendationsToPDF(
  learnersWithRecommendations,
  currentCategory,
  totalRecommendations,
  selectedRole,
  searchTerm
) {
  // Filter learners (similar to filterRecommendations)
  const filteredLearners = learnersWithRecommendations.filter((learner) => {
    const matchesSearchTerm = learner.name.toLowerCase().includes(searchTerm);
    const matchesRole = !selectedRole || learner.role === selectedRole;
    return matchesSearchTerm && matchesRole;
  });

  // Create a container for PDF content
  const pdfContent = document.createElement("div");
  pdfContent.style.padding = "40px";
  pdfContent.style.fontFamily = "Arial, sans-serif";
  pdfContent.style.color = "#1F2937";
  pdfContent.style.maxWidth = "800px";
  pdfContent.style.margin = "0 auto";

  // Header Section with ICG Logo
  const headerSection = document.createElement("div");
  headerSection.style.textAlign = "center";
  headerSection.style.marginBottom = "40px";
  headerSection.style.position = "relative";

  // Create logo container
  const logoContainer = document.createElement("div");
  logoContainer.style.marginBottom = "20px";

  // Create logo image element
  const logoImg = document.createElement("img");
  logoImg.src = "icglogo.png";
  logoImg.alt = "ICG Logo";
  logoImg.style.height = "60px";
  logoImg.style.width = "auto";
  logoImg.style.maxWidth = "200px";
  logoImg.style.objectFit = "contain";
  logoContainer.appendChild(logoImg);

  headerSection.appendChild(logoContainer);

  // Add title and content
  const titleContent = document.createElement("div");
  titleContent.innerHTML = `
    <h1 style="color: #1F2937; margin-bottom: 10px; font-size: 24px;">Course Recommendations Report</h1>
    <h2 style="color: #3B82F6; margin-bottom: 10px; font-size: 20px;">${currentCategory} Pathway</h2>
    <p style="margin: 5px 0;">Total Learners: ${filteredLearners.length}</p>
    <p style="margin: 5px 0;">Total Recommendations: ${filteredLearners.reduce(
      (total, learner) =>
        total +
        learner.corePathRecommendations.length +
        learner.otherPathRecommendations.length,
      0
    )}</p>
  `;
  headerSection.appendChild(titleContent);

  pdfContent.appendChild(headerSection);

  const divider = document.createElement("hr");
  divider.style.margin = "20px 0";
  divider.style.border = "1px solid #E5E7EB";
  pdfContent.appendChild(divider);

  // Recommendations Content
  filteredLearners.forEach((learner, index) => {
    const learnerSection = document.createElement("div");
    learnerSection.style.marginBottom = "40px";
    learnerSection.style.pageBreakInside = "avoid";
    if (index === filteredLearners.length - 1) {
      learnerSection.style.marginBottom = "60px";
    }

    const learnerName = document.createElement("h3");
    learnerName.style.color = "#2563EB";
    learnerName.style.fontSize = "18px";
    learnerName.style.marginBottom = "15px";
    learnerName.textContent = `${learner.name} (${learner.role})`;
    learnerSection.appendChild(learnerName);

    const recommendationsList = document.createElement("ul");
    recommendationsList.style.listStyleType = "disc";
    recommendationsList.style.marginLeft = "20px";
    recommendationsList.style.marginTop = "15px";
    recommendationsList.style.paddingRight = "20px";

    // Core recommendations
    learner.corePathRecommendations.forEach((rec, recIndex) => {
      const listItem = document.createElement("li");
      listItem.style.marginBottom = "15px";
      listItem.style.color = "#374151";

      const courseName = document.createElement("div");
      courseName.style.fontWeight = "bold";
      courseName.style.marginBottom = "6px";
      courseName.style.color = rec.assessmentType === 'soft skills' ? "#6B21A8" : "#68C692";
      courseName.textContent = rec.course;
      listItem.appendChild(courseName);

      const courseReason = document.createElement("div");
      courseReason.style.fontSize = "14px";
      courseReason.style.color = "#6B7280";
      courseReason.style.paddingRight = "10px";
      courseReason.style.lineHeight = "1.4";
      courseReason.textContent = rec.reason;
      listItem.appendChild(courseReason);

      recommendationsList.appendChild(listItem);
    });

    // Other path recommendations
    learner.otherPathRecommendations.forEach((rec, recIndex) => {
      const listItem = document.createElement("li");
      listItem.style.marginBottom = "15px";
      listItem.style.color = "#374151";

      const courseName = document.createElement("div");
      courseName.style.fontWeight = "bold";
      courseName.style.marginBottom = "6px";
      courseName.style.color = rec.assessmentType === 'soft skills' ? "#6B21A8" : "#68C692";
      courseName.textContent = `${rec.course || rec.courseName} (${rec.learningPath})`;
      listItem.appendChild(courseName);

      const courseReason = document.createElement("div");
      courseReason.style.fontSize = "14px";
      courseReason.style.color = "#6B7280";
      courseReason.style.paddingRight = "10px";
      courseReason.style.lineHeight = "1.4";
      courseReason.textContent = rec.reason || rec.justification;
      listItem.appendChild(courseReason);

      recommendationsList.appendChild(listItem);
    });

    learnerSection.appendChild(recommendationsList);
    pdfContent.appendChild(learnerSection);
  });

  document.body.appendChild(pdfContent);

  const opt = {
    margin: [1, 1, 1.25, 1],
    filename: "Course_Recommendations.pdf",
    image: { type: "jpeg", quality: 1 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      letterRendering: true,
      scrollY: -window.scrollY,
      windowWidth: document.documentElement.offsetWidth,
    },
    jsPDF: {
      unit: "in",
      format: "letter",
      orientation: "portrait",
      compress: true,
    },
    pagebreak: {
      mode: ["avoid-all", "css", "legacy"],
      after: ".page-break-after",
      before: ".page-break-before",
      avoid: ["li", "div"],
    },
  };

  try {
    await html2pdf().from(pdfContent).set(opt).save();
  } catch (error) {
    console.error("Error generating PDF:", error);
  } finally {
    document.body.removeChild(pdfContent);
  }
}

//------------------------------------------------------
// OPEN SHARED CALENDAR (Outlook/Office365 Link)
//------------------------------------------------------
function openSharedCalendar() {
  window.open(
    "https://outlook.office365.com/book/<EMAIL>/",
    "_blank"
  );
}

//------------------------------------------------------
// MAKE initializeDetail ETC. GLOBALLY ACCESSIBLE
//------------------------------------------------------
window.initializeDetail = initializeDetail;
window.addEventListeners = addEventListeners;
window.loadEnrollmentPage = loadEnrollmentPage;
