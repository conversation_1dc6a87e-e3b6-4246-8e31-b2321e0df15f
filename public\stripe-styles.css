/* Stripe modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.95);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-title {
  font-size: 24px;
  font-weight: 600;
  color: #121c41;
}

.close-modal-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s;
}

.close-modal-button:hover {
  color: #1f2937;
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.credit-package {
  border: 2px solid #E5E7EB;
  border-radius: 8px;
  padding: 20px;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s, border-color 0.2s, background-color 0.2s;
}

.credit-package:hover {
  transform: translateY(-4px);
  border-color: #D1D5DB;
}

.credit-package.selected {
  border-color: #68C692;
  background-color: #E5F2E5;
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #68C692;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.package-title {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 12px;
  color: #121c41;
}

.package-highlight {
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  color: #68C692;
  margin-bottom: 8px;
}

.package-description {
  text-align: center;
  color: #4B5563;
  font-size: 14px;
  margin-bottom: 16px;
  min-height: 40px;
}

.package-price {
  text-align: center;
  margin-bottom: 16px;
}

.total-price {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #121c41;
}

.price-per-credit {
  display: block;
  font-size: 14px;
  color: #6B7280;
  margin-top: 4px;
}

.select-package-btn {
  width: 100%;
  padding: 8px 16px;
  background-color: #F3F4F6;
  color: #1F2937;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-package-btn:hover {
  background-color: #E5E7EB;
}

.select-package-btn.selected {
  background-color: #68C692;
  color: white;
}

.stripe-checkout-button {
  width: 100%;
  padding: 12px 24px;
  background-color: #68C692;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.stripe-checkout-button:hover {
  background-color: #8DCE8C;
}

/* Notification styles */
.payment-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 24px;
  border-radius: 6px;
  z-index: 1010;
  max-width: 350px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateX(120%);
  transition: transform 0.3s ease;
}

.payment-notification.show {
  transform: translateX(0);
}

.payment-notification.success {
  background-color: #68C692;
  color: white;
}

.payment-notification.error {
  background-color: #FEE2E2;
  color: #991B1B;
  border: 1px solid #FCA5A5;
}

.payment-notification.info {
  background-color: #E0F2FE;
  color: #0C4A6E;
  border: 1px solid #BAE6FD;
}

/* Hide elements */
.hidden {
  display: none;
}
