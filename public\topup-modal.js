(function(global) {
  // Top-up package definitions
  const topupPackages = {
    topup100: {
      id: 'topup100',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI0L8F65CEkirmgGw2jDw'  // Live mode price ID
        : 'price_1RMP2fPqOZsaOO5kk3cePRnn',  // Test mode price ID
      name: 'TopUp 100',
      credits: 100,
      price: 999,
      popular: true,
      highlight: "Most Popular",
      description: "Add 100 assessments to your account",
      features: [
        'One-time purchase',
        'Adds 100 assessments to your account',
        'Extends dashboard access for 12 months',
        'No recurring charges',
        'Assessments expire after 12 months'
      ],
      cta: 'Purchase Now'
    },
    topup250: {
      id: 'topup250',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjHuL8F65CEkirXNMZ4UnJ'  // Live mode price ID
        : 'price_1RMP2hPqOZsaOO5k8YEHnxu4',  // Test mode price ID
      name: 'TopUp 250',
      credits: 250,
      price: 1999,
      highlight: "Best Value",
      description: "Add 250 assessments to your account",
      features: [
        'One-time purchase',
        'Adds 250 assessments to your account',
        'Extends dashboard access for 12 months',
        'No recurring charges',
        'Assessments expire after 12 months'
      ],
      cta: 'Purchase Now'
    },
    topup500: {
      id: 'topup500',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjHpL8F65CEkirTkzlPtEB'  // Live mode price ID
        : 'price_1RMP2kPqOZsaOO5kRLmLuPwu',  // Test mode price ID
      name: 'TopUp 500',
      credits: 500,
      price: 2999,
      highlight: "For larger teams",
      description: "Add 500 assessments to your account",
      features: [
        'One-time purchase',
        'Adds 500 assessments to your account',
        'Extends dashboard access for 12 months',
        'No recurring charges',
        'Assessments expire after 12 months'
      ],
      cta: 'Purchase Now'
    }
  };

  // Make the topup packages available globally
  window.topupPackages = topupPackages;

  // State management
  let isModalInitialized = false;
  let selectedPackage = null;
  let isClosing = false;
  let stripeInstance = null;

  // DOM Elements
  let modalOverlay;
  let modalContent;

  async function loadStripeJs() {
    if (window.Stripe) {
      return window.Stripe;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => {
        if (window.Stripe) {
          resolve(window.Stripe);
        } else {
          reject(new Error('Stripe.js failed to load'));
        }
      };
      script.onerror = () => reject(new Error('Failed to load Stripe.js'));
      document.head.appendChild(script);
    });
  }

  async function initializeStripe() {
    if (stripeInstance) {
      return stripeInstance;
    }

    try {
      const Stripe = await loadStripeJs();
      stripeInstance = Stripe(window.STRIPE_PUBLISHABLE_KEY);
      return stripeInstance;
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      throw error;
    }
  }

  // Check if user has an active subscription (not free trial) - app is now free
  async function checkUserEligibility() {
    console.log('User eligibility check bypassed - app is now free');
    return { eligible: false, reason: 'App is now free - no top-ups needed' };
  }

  // Create modal HTML
  function createModalHTML() {
    const title = 'Add More Assessments';
    const subtitle = 'Purchase additional assessments without changing your subscription';

    // Generate package HTML
    const packagesHTML = Object.values(topupPackages).map(pkg => {
      const formattedPrice = pkg.price.toLocaleString('en-GB');

      return `
        <div class="topup-package ${pkg.popular ? 'popular' : ''}" data-package-id="${pkg.id}">
          ${pkg.popular ? '<div class="popular-badge">Popular</div>' : ''}
          <div class="package-header">
            <h3 class="package-name">${pkg.name}</h3>
            <div class="package-price">
              <span class="currency">£</span>
              <span class="amount">${formattedPrice}</span>
            </div>
            <div class="package-description">${pkg.description}</div>
          </div>
          <ul class="package-features">
            ${pkg.features.map(feature => `
              <li>
                <svg class="feature-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                ${feature}
              </li>
            `).join('')}
          </ul>
          <button class="package-cta-button">${pkg.cta}</button>
        </div>
      `;
    }).join('');

    return `
      <div class="topup-modal-overlay">
        <div class="topup-modal-content">
          <div class="topup-modal-header">
            <div class="topup-modal-title">
              <h2>${title}</h2>
              <p class="topup-modal-subtitle">${subtitle}</p>
            </div>
            <button class="topup-modal-close">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          <div class="topup-packages-grid">
            ${packagesHTML}
          </div>

          <div class="topup-footer">
            <p class="topup-note">Purchasing additional assessments will extend your dashboard access for 12 months from the date of purchase.</p>
            <p class="topup-contact">
              If you need more than 500 assessments or would like to explore personalising SkillsAssess, please
              <a href="#" class="contact-link">get in touch</a>.
            </p>
          </div>
        </div>
      </div>
    `;
  }

  // Initialize CSS
  function injectCSS() {
    if (document.getElementById('topup-modal-styles')) return;

    const styleEl = document.createElement('style');
    styleEl.id = 'topup-modal-styles';
    styleEl.textContent = `
      .topup-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.8);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .topup-modal-content {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        width: 95%;
        max-width: 1200px;
        max-height: 90vh;
        overflow-y: auto;
        padding: 32px;
        opacity: 0;
        transform: scale(0.95);
        transition: transform 0.3s ease, opacity 0.3s ease;
      }

      .topup-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24px;
      }

      .topup-modal-title h2 {
        font-size: 28px;
        font-weight: 700;
        color: #121C41;
        margin: 0 0 8px 0;
      }

      .topup-modal-subtitle {
        font-size: 16px;
        color: #4B5563;
        margin: 0;
      }

      .topup-modal-close {
        background: none;
        border: none;
        cursor: pointer;
        color: #6B7280;
        padding: 4px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease, color 0.2s ease;
      }

      .topup-modal-close:hover {
        background-color: #F3F4F6;
        color: #1F2937;
      }

      .topup-packages-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
        margin-bottom: 24px;
      }

      .topup-package {
        border: 1px solid #E5E7EB;
        border-radius: 8px;
        padding: 24px;
        display: flex;
        flex-direction: column;
        position: relative;
        transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
      }

      .topup-package:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #D1D5DB;
      }

      .topup-package.popular {
        border-color: #1547BB;
        box-shadow: 0 4px 6px -1px rgba(21, 71, 187, 0.1), 0 2px 4px -1px rgba(21, 71, 187, 0.06);
      }

      .popular-badge {
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #1547BB;
        color: white;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 12px;
      }

      .package-header {
        margin-bottom: 16px;
        text-align: center;
      }

      .package-name {
        font-size: 20px;
        font-weight: 700;
        color: #121C41;
        margin: 0 0 8px 0;
      }

      .package-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: 8px;
      }

      .currency {
        font-size: 20px;
        font-weight: 600;
        color: #121C41;
        margin-right: 2px;
      }

      .amount {
        font-size: 40px;
        font-weight: 700;
        color: #121C41;
        line-height: 1;
      }

      .package-description {
        font-size: 14px;
        color: #6B7280;
        margin-top: 8px;
      }

      .package-features {
        list-style: none;
        padding: 0;
        margin: 0 0 24px 0;
        flex-grow: 1;
      }

      .package-features li {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        color: #4B5563;
      }

      .feature-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        color: #1547BB;
        flex-shrink: 0;
      }

      .package-cta-button {
        background-color: #1547BB;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 16px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
        width: 100%;
      }

      .package-cta-button:hover {
        background-color: #0D3592;
      }

      .topup-footer {
        border-top: 1px solid #E5E7EB;
        padding-top: 24px;
        font-size: 14px;
        color: #6B7280;
      }

      .topup-note {
        margin-bottom: 16px;
      }

      .contact-link {
        color: #1547BB;
        text-decoration: none;
        font-weight: 500;
      }

      .contact-link:hover {
        text-decoration: underline;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .topup-modal-content {
          padding: 24px 16px;
        }

        .topup-packages-grid {
          grid-template-columns: 1fr;
        }

        .topup-modal-title h2 {
          font-size: 22px;
        }

        .amount {
          font-size: 36px;
        }

        /* Handle badge positioning on mobile */
        .popular-badge {
          font-size: 10px;
          padding: 3px 10px;
        }
      }
    `;

    document.head.appendChild(styleEl);
  }

  // Show modal - app is now free
  async function showTopupModal() {
    console.log('Top-up modal bypassed - app is now free');
    showNotification('App is now free - no top-ups needed!', 'info');
    return { success: false, reason: 'App is now free' };
  }

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.topup-modal-overlay');
    if (existingModal) {
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Create modal
    injectCSS();
    const modalHTML = createModalHTML();
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.topup-modal-overlay');
    modalContent = document.querySelector('.topup-modal-content');

    // Initialize event listeners
    initializeEventListeners();

    isModalInitialized = true;

    // Show modal with animation
    setTimeout(() => {
      if (isClosing) return;

      modalOverlay.style.opacity = '1';
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);

    return new Promise(resolve => {
      // This will be resolved when the user makes a selection or closes the modal
      window.topupModalResult = resolve;
    });
  }

  // Initialize event listeners
  function initializeEventListeners() {
    // Close button
    const closeButton = document.querySelector('.topup-modal-close');
    closeButton.addEventListener('click', hideTopupModal);

    // Click outside to close
    modalOverlay.addEventListener('click', (event) => {
      if (event.target === modalOverlay) {
        hideTopupModal();
      }
    });

    // Package selection
    const packages = document.querySelectorAll('.topup-package');
    packages.forEach(pkg => {
      const packageId = pkg.getAttribute('data-package-id');
      const ctaButton = pkg.querySelector('button');

      ctaButton.addEventListener('click', () => {
        handlePackageSelection(pkg, packageId);
      });

      // Also make the whole card clickable
      pkg.addEventListener('click', (event) => {
        // Only trigger if the click wasn't on the button (as that has its own handler)
        if (!event.target.closest('button')) {
          handlePackageSelection(pkg, packageId);
        }
      });
    });

    // Contact link
    const contactLink = document.querySelector('.contact-link');
    contactLink.addEventListener('click', (event) => {
      event.preventDefault();
      hideTopupModal();
      // Open email client with pre-filled subject
      window.location.href = 'mailto:<EMAIL>?subject=Custom%20SkillsAssess%20Inquiry';
    });
  }

  // Handle package selection
  async function handlePackageSelection(packageElement, packageId) {
    // Update UI to show selection
    document.querySelectorAll('.topup-package').forEach(pkg => {
      pkg.classList.remove('selected');
    });
    packageElement.classList.add('selected');

    // Process the selection
    try {
      await processTopupCheckout(packageId);
    } catch (error) {
      console.error('Error processing package selection:', error);
      showNotification('There was an error processing your selection. Please try again.', 'error');
    }
  }

  // Process top-up checkout
  async function processTopupCheckout(packageId) {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to purchase top-ups.', 'error');
      return;
    }

    const package = topupPackages[packageId];
    if (!package) {
      showNotification('Invalid package selected.', 'error');
      return;
    }

    try {
      // Initialize Stripe if not already done
      await initializeStripe();

      showNotification('Preparing checkout...', 'info');

      // Call the server endpoint for one-time payment
      const response = await fetch('/create-topup-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priceId: package.priceId,
          userId: user.email,
          credits: package.credits
        })
      });

      const session = await response.json();

      if (session.error) {
        throw new Error(session.error);
      }

      // Redirect to Stripe Checkout
      const result = await stripeInstance.redirectToCheckout({
        sessionId: session.id
      });

      if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (error) {
      console.error('Error redirecting to Stripe:', error);
      showNotification('Could not process your request. Please try again later.', 'error');
    }
  }

  // Hide modal
  async function hideTopupModal(result = { cancelled: true }) {
    isClosing = true;

    // Animate closing
    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // Resolve promise if it exists
        if (window.topupModalResult) {
          window.topupModalResult(result);
          window.topupModalResult = null;
        }
      }, 300);
    }
  }

  // Show notification
  function showNotification(message, type = 'success') {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.topup-notification');
    existingNotifications.forEach(notification => {
      notification.remove();
    });

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `topup-notification ${type}`;
    notification.textContent = message;

    // Add styles if not already present
    if (!document.getElementById('topup-notification-styles')) {
      const style = document.createElement('style');
      style.id = 'topup-notification-styles';
      style.textContent = `
        .topup-notification {
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 16px 24px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          z-index: 1010;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          transform: translateX(120%);
          transition: transform 0.3s ease;
          max-width: 350px;
        }

        .topup-notification.show {
          transform: translateX(0);
        }

        .topup-notification.success {
          background-color: #1547BB;
          color: white;
        }

        .topup-notification.error {
          background-color: #FEE2E2;
          color: #991B1B;
          border: 1px solid #FCA5A5;
        }

        .topup-notification.info {
          background-color: #E0F2FE;
          color: #0C4A6E;
          border: 1px solid #BAE6FD;
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // Remove after delay
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (document.body.contains(notification)) {
          notification.remove();
        }
      }, 300);
    }, 4000);
  }

  // Public API
  global.TopupModal = {
    show: showTopupModal,
    hide: hideTopupModal
  };
})(typeof window !== 'undefined' ? window : global);
