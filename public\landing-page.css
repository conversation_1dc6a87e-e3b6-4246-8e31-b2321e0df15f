/* Landing Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

body {
    background-image: url('ICGlogin.png');
    background-size: cover;
    font-family: 'Inter', 'Montserrat', sans-serif;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.landing-container {
    width: 90%;
    max-width: 480px;
    padding: 2.5rem 2rem;
    background-color: rgba(255, 255, 255, 0.92);
    border-radius: 16px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 20px 30px -10px rgba(0, 0, 0, 0.2), 0 10px 20px -5px rgba(0, 0, 0, 0.04);
    margin: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.18);
    transition: all 0.3s ease;
    box-sizing: border-box; /* Ensure padding is included in width */
    overflow: visible; /* Changed from hidden to visible to prevent input cutoff */
    position: relative; /* Added to ensure proper containment */
}

.landing-heading {
    text-align: center;
    margin-bottom: 2.25rem;
}

.landing-heading h3 {
    font-size: 1.55rem;
    font-weight: 600;
    color: #2c4da5f3;
    margin-bottom: 0.75rem;
    letter-spacing: -0.02em;
    line-height: 1.3;
}

.landing-heading p {
    font-size: 1.05rem;
    color: #4b5563;
    margin-top: 0;
    line-height: 1.5;
    font-weight: 600; /* Changed from 400 to 600 for semi-bold */
}

.input-group {
    margin-bottom: 1.5rem;
    position: relative;
    width: 100%; /* Added to ensure inputs stay within container */
}

.input-group input {
    width: 100%;
    box-sizing: border-box; /* Include padding in width calculation */
    padding: 0.9rem 1.1rem;
    padding-left: 2.5rem;
    border: 1px solid rgba(209, 213, 219, 0.5);
    border-radius: 10px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    max-width: 100%; /* Added to prevent overflow */
}

.input-group input:focus {
    outline: none;
    border-color: #2c4da5f3;
    box-shadow: 0 0 0 4px rgba(44, 77, 165, 0.1);
    background-color: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

.error-message {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: none;
}

.submit-button {
    margin-top: 2rem;
    text-align: center;
    width: 100%; /* Added to ensure button stays within container */
}

.submit-button button {
    width: 100%;
    padding: 1rem 1.5rem;
    background-color: #2c4da5f3;
    background-image: linear-gradient(135deg, #2c4da5f3, #3b5fc9);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(44, 77, 165, 0.25);
    letter-spacing: 0.01em;
}

.submit-button button:hover {
    background-image: linear-gradient(135deg, #2c4da5f3, #4b6fd9);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(44, 77, 165, 0.35);
}

.submit-button button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(44, 77, 165, 0.2);
}

.logo-container {
    text-align: center;
    margin-bottom: 1.75rem;
}

.logo-container img {
    height: 2rem; /* Reduced logo size */
    width: auto;
    opacity: 0.95;
}

/* Video container styles */
.video-container {
    width: 100%;
    margin-bottom: 2.25rem;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    background-color: rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 12px;
}

/* Enhanced video container for larger screens */
@media (min-width: 1024px) {
    .video-page-container .video-container {
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
        box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.15);
    }
}

@media (min-width: 1280px) {
    .video-page-container .video-container {
        max-width: 85%;
    }
}

/* Loading overlay */
#loading-overlay {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.loading-animation {
    width: 45px;
    height: 45px;
}

.loading-text {
    color: white;
    margin-top: 1rem;
    font-size: 0.75rem;
    letter-spacing: 1px;
}

/* Input icons */
.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 0.9rem;
    z-index: 1;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.input-group input:focus ~ .input-icon,
.input-group input:not(:placeholder-shown) ~ .input-icon {
    color: #2c4da5f3;
    opacity: 1;
}

/* Privacy note */
.privacy-note {
    text-align: center;
    margin-top: 1.25rem;
    font-size: 0.8rem;
    color: #6b7280;
    opacity: 0.8;
    width: 100%; /* Added to ensure it stays within container */
}

/* Benefits list */
.benefits-list {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    color: #4b5563;
}

.benefit-item i {
    color: #2c4da5f3;
    font-size: 1rem;
}

/* Enhanced benefits list for video page on larger screens */
@media (min-width: 768px) {
    .video-page-container .benefits-list {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .video-page-container .benefit-item {
        flex: 0 0 auto;
    }
}

@media (min-width: 1024px) {
    .video-page-container .submit-button {
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }
}

/* Form styles */
form {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center; /* Added to center form contents */
}

/* Responsive adjustments */
/* Large screens - expand video container */
@media (min-width: 1024px) {
    .landing-container {
        max-width: 80%;
        width: 80%;
    }

    /* Special styling for landing-page-2.html (video page) */
    .video-page-container {
        max-width: 90%;
        width: 90%;
    }
}

/* Medium screens */
@media (min-width: 768px) and (max-width: 1023px) {
    .landing-container {
        max-width: 700px;
        width: 85%;
    }

    /* Special styling for landing-page-2.html (video page) */
    .video-page-container {
        max-width: 80%;
        width: 80%;
    }
}

/* Small screens */
@media (max-width: 640px) {
    .landing-container {
        padding: 1.5rem 1.25rem;
        margin: 1rem;
        width: 95%;
    }

    .landing-heading h3 {
        font-size: 1.2rem;
    }

    .landing-heading p {
        font-size: 0.875rem;
        font-weight: 600; /* Maintained semi-bold weight for mobile */
    }

    .benefit-item {
        font-size: 0.85rem;
    }

    .input-group input {
        padding: 0.8rem 1rem 0.8rem 2.3rem;
        font-size: 0.9rem;
    }

    .input-icon {
        left: 0.8rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
    from { transform: translateY(15px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

/* Add subtle animations to form elements */
form .input-group:nth-of-type(1) { animation: slideUp 0.4s ease-out 0.1s both; }
form .input-group:nth-of-type(2) { animation: slideUp 0.4s ease-out 0.2s both; }
form .input-group:nth-of-type(3) { animation: slideUp 0.4s ease-out 0.3s both; }
form .input-group:nth-of-type(4) { animation: slideUp 0.4s ease-out 0.4s both; }
form .input-group:nth-of-type(5) { animation: slideUp 0.4s ease-out 0.5s both; }
form .submit-button { animation: slideUp 0.4s ease-out 0.6s both; }
.privacy-note { animation: slideUp 0.4s ease-out 0.7s both; }
.benefits-list { animation: slideUp 0.4s ease-out 0.5s both; }
.benefit-item:nth-of-type(1) { animation: slideUp 0.4s ease-out 0.6s both; }
.benefit-item:nth-of-type(2) { animation: slideUp 0.4s ease-out 0.7s both; }
.benefit-item:nth-of-type(3) { animation: slideUp 0.4s ease-out 0.8s both; }