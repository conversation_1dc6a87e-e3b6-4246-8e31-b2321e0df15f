<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Include Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#68C692',
                        secondary: '#A2D6A0',
                    }
                }
            }
        }
    </script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <style>
        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        /* Apply font to the body */
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
        }


        /* Glass container styling */
        .glass-container {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
        }

        /* Enhanced text styling with accessibility improvements */
        :root {
            /* Accessible text colors for WCAG 2.1 AA compliance */
            --text-dark: #2D3748;        /* Dark gray for body text on light backgrounds */
            --text-medium: #4A5568;      /* Medium gray for secondary text */
            --text-light: #718096;       /* Light gray for tertiary text */
            --text-on-dark: #FFFFFF;     /* White text for dark green backgrounds */
            --text-on-light: #2D3748;    /* Dark text for light green backgrounds */
            --text-muted: #9CA3AF;       /* Muted text for placeholders and disabled states */
        }

        .text-secondary {
            color: #68C692 !important;
        }

        .text-lg.text-gray-700 {
            font-size: 0.9rem;
            color: var(--text-medium) !important;
        }

        .green-text {
            color: #68C692;
        }

        /* Link card styling */
        .link-card {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(104, 198, 146, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .link-card:hover {
            box-shadow: 0 4px 20px rgba(104, 198, 146, 0.15);
            transform: translateY(-2px);
        }

        .copy-button {
            background-color: #2D5A3D;
            transition: all 0.3s ease;
            border: 2px solid #1F4A2A;
            box-shadow: 0 2px 4px rgba(45, 90, 61, 0.3);
        }

        .copy-button:hover {
            background-color: #1F4A2A;
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(45, 90, 61, 0.4);
        }

        .copy-button.copied {
            background-color: #4CAF50;
            border-color: #45A049;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }

        .copy-button:focus {
            outline: none;
            ring: 2px solid #68C692;
            ring-offset: 2px;
        }

        /* Enhanced icon visibility */
        .copy-button svg {
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            stroke-width: 2.5;
        }

        .copy-button:hover svg {
            stroke-width: 3;
        }

        /* Tooltip for better UX */
        .copy-button {
            position: relative;
        }

        .copy-button::after {
            content: 'Copy link';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #1F2937;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }

        .copy-button:hover::after {
            opacity: 1;
        }

        .copy-button.copied::after {
            content: 'Copied!';
            background-color: #4CAF50;
        }

        .link-input {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: var(--text-dark);
        }
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .active-tab {
            border-bottom-color: #68C692;
            font-weight: bold;
        }

        /* Invitee List Styles */
        .invitees-list {
            overflow-x: auto;
            display: flex;
            flex-wrap: nowrap;
            gap: 1rem;
            padding-bottom: 0.5rem;
        }

        .invitee-item {
            flex: 0 0 auto;
            background-color: #f9fafb;
            padding: 0.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            font-size: 0.85rem;
            min-width: 180px; /* Adjust as needed */
        }

        .invitee-item:hover {
            background-color: #f3f4f6;
        }

        /* Improve aesthetics */
        .internal-button {
            transition: background-color 0.3s ease;
        }

        .internal-button.add-button {
            background-color: #4CAF50; /* Green */
        }

        .internal-button.add-button:hover {
            background-color: #45A049;
        }

        .internal-button.upload-button {
            background-color: #68C692; /* Green */
        }

        .internal-button.upload-button:hover {
            background-color: #8DCE8C;
        }

        /* Hover effects for summary cards */
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* Adjust form inputs */
        .form-group input {
            background-color: #fff;
        }

        .form-group input:focus {
            border-color: #68C692;
            box-shadow: 0 0 0 1px #68C692;
        }

        /* Reduce spacing in form sections */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        /* Adjust spacing for buttons */
        .form-group.mt-6 {
            margin-top: 1.5rem;
        }

        /* Shake animation */
        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }

        .shake {
            animation: shake 0.5s;
        }
        #detailsContainer {
        transition: max-height 0.3s ease-out;
        max-height: 0;
        overflow: hidden;
        }

    #detailsContainer.hidden {
    display: none;
        }
        #detailsContainer ul {
     max-height: 200px;
    overflow-y: auto;
    }

    #detailsContainer li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
    }

    #detailsContainer li:last-child {
    border-bottom: none;
    }

    .summary {
  color: #333;
  max-width: 800px;
  margin: 0 auto;
  padding: 30px;
  background-color: #f0f4f8;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.summary-header {
  border-bottom: 2px solid #d1e1f1;
  padding-bottom: 15px;
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-header h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1a5f7a;
  margin: 0;
}

.summary-header button {
  background: none;
  border: none;
  color: #3498db;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.summary-header button:hover {
  color: #2980b9;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
}

.summary-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.summary-card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.summary-card h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1a5f7a;
  margin: 0 0 10px 0;
}

.summary-card p {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
}

#detailsContainer {
  margin-top: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

#detailsContainer h5 {
  font-size: 1.1rem;
  color: #1a5f7a;
  margin-bottom: 15px;
  border-bottom: 1px solid #d1e1f1;
  padding-bottom: 10px;
}

#detailsContainer ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.8;
  color: #34495e;
}

#detailsContainer ul li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

#detailsContainer ul li::before {
  content: '•';
  color: #3498db;
  position: absolute;
  left: 0;
  top: 0;
}
.summary-card.emails-sent h4,
.summary-card.emails-failed h4 {
  color: #1a5f7a;
}
/* Fix for truncated email domains in Sent Emails section */
#sentEmailsList li,
#failedEmailsList li,
.summary-grid .summary-card span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: inline-block;
}

/* Fix for the email domain display in the invitation summary */
#inviteContainer {
  position: relative;
  overflow: hidden;
}

#sentEmailsList {
  width: 100%;
  word-break: break-all;
}

/* Email list styling */
.sent-emails {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

/* Fix summary cards on smaller screens */
@media (max-width: 768px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .summary-card {
    padding: 0.75rem;
  }

  /* Better handling of email text in the summary cards */
  .summary-card .text-sm {
    font-size: 0.75rem;
  }
}

/* Improved responsive styling for the entire invitation view */
@media (max-width: 640px) {
  .glass-container {
    padding: 1.25rem;
    max-width: 100%;
  }

  /* Ensure email text doesn't overflow container */
  #detailsContainer ul li {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
    padding-right: 0.5rem;
  }
}

  .notification {
            position: fixed;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 6px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 9999;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            font-weight: 500;
        }

        .notification.success {
            background-color: #68C692;
            color: white;
        }

        .notification.error {
            background-color: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FCA5A5;
        }

        .notification.warning {
            background-color: #FEF3C7;
            color: #92400E;
            border: 1px solid #FCD34D;
        }
        .credit-package {
    transition: all 0.3s ease;
}

.credit-package:hover {
    transform: translateY(-2px);
}

.select-package-btn {
    transition: all 0.2s ease;
}

.credit-prompt {
    transition: all 0.3s ease;
}

/* Assessment Type Selector Styles */
.assessment-selector-container {
    display: flex;
    justify-content: center;
    margin: 2rem 0 1rem;
}

.assessment-selector {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 12px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assessment-type {
    padding: 0.7rem 1.2rem;  /* Slightly reduced padding */
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;  /* Reduced from 0.9rem */
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-medium);  /* Default to accessible gray for inactive state */
}

.assessment-type:hover {
    background-color: rgba(104, 198, 146, 0.1);
}

.assessment-type.active-assessment {
    background-color: #68C692;
    color: white;  /* Only active state is white */
}

.assessment-type svg {
    width: 1.2rem;  /* Slightly reduced from 1.25rem */
    height: 1.2rem;  /* Slightly reduced from 1.25rem */
}
    </style>
</head>
<body class="bg-gray-100">
    <div class="glass-container">
        <!-- Header -->
        <div class="flex flex-col items-center mb-8">
            <div class="flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-secondary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              <h1 class="text-xl font-semibold text-secondary">Assessment Links</h1>
            </div>
            <p class="text-base text-gray-700">Share assessment links with your team members</p>
          </div>

        <!-- Assessment Links Section -->
        <div class="assessment-links-container">
            <!-- Digital Skills Assessment Link -->
            <div class="link-card">
                <div class="flex items-center mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25" />
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-800">Digital Skills Assessment</h3>
                </div>
                <p class="text-sm text-gray-600 mb-4">Evaluate digital competencies and technical skills</p>
                <div class="flex gap-2">
                    <input
                        type="text"
                        id="digitalSkillsLink"
                        class="link-input flex-1"
                        readonly
                        value=""
                        placeholder="Generating link..."
                    >
                    <button
                        id="copyDigitalLink"
                        class="copy-button px-4 py-2 text-white rounded-lg font-medium"
                        data-link-type="digital"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- AI Skills Assessment Link -->
            <div class="link-card">
                <div class="flex items-center mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-800">AI Skills Assessment</h3>
                </div>
                <p class="text-sm text-gray-600 mb-4">Assess artificial intelligence and machine learning capabilities</p>
                <div class="flex gap-2">
                    <input
                        type="text"
                        id="aiSkillsLink"
                        class="link-input flex-1"
                        readonly
                        value=""
                        placeholder="Generating link..."
                    >
                    <button
                        id="copyAiLink"
                        class="copy-button px-4 py-2 text-white rounded-lg font-medium"
                        data-link-type="ai"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Soft Skills Assessment Link -->
            <div class="link-card">
                <div class="flex items-center mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-800">Soft Skills Assessment</h3>
                </div>
                <p class="text-sm text-gray-600 mb-4">Evaluate interpersonal and communication skills</p>
                <div class="flex gap-2">
                    <input
                        type="text"
                        id="softSkillsLink"
                        class="link-input flex-1"
                        readonly
                        value=""
                        placeholder="Generating link..."
                    >
                    <button
                        id="copySoftLink"
                        class="copy-button px-4 py-2 text-white rounded-lg font-medium"
                        data-link-type="soft"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Instructions Section -->
        <div class="mt-8 p-6 bg-green-50 rounded-lg border border-green-200">
            <div class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                    <h4 class="text-sm font-semibold text-green-800 mb-2">How to use these links:</h4>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li>• Click the copy button next to any assessment link</li>
                        <li>• Share the copied link with your team members via email, chat, or any communication platform</li>
                        <li>• Each link leads to a specific assessment type tailored to different skill areas</li>
                        <li>• Team members can complete assessments at their own pace</li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

    <!-- Include User Journey Tracker -->
    <script src="user-journey-tracker.js"></script>

    <!-- Now load invite.js -->
    <script src="invite.js"></script>
</body>
</html>
