<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICG - Demo Request</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="landing-page.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    <!-- <PERSON><PERSON> for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.14/lottie.min.js"></script>
</head>
<body>
    <div class="landing-container fade-in">
        <div class="logo-container">
            <img src="icglogo.png" alt="ICG Logo">
        </div>
        <div class="landing-heading">
            <h3>Thanks for showing your interest in ICG</h3>
            <p>Fill in your information below to watch our demo and start your free trial today</p>
        </div>

        <form id="demoForm">
            <div class="input-group">
                <input type="text" id="firstname" placeholder="First Name" required>
                <i class="input-icon fas fa-user"></i>
                <div id="firstnameError" class="error-message"></div>
            </div>

            <div class="input-group">
                <input type="text" id="lastname" placeholder="Last Name" required>
                <i class="input-icon fas fa-user"></i>
                <div id="lastnameError" class="error-message"></div>
            </div>

            <div class="input-group">
                <input type="email" id="email" placeholder="Work Email Address" required>
                <i class="input-icon fas fa-envelope"></i>
                <div id="emailError" class="error-message"></div>
            </div>

            <div class="input-group">
                <input type="text" id="company" placeholder="Company Name" required>
                <i class="input-icon fas fa-building"></i>
                <div id="companyError" class="error-message"></div>
            </div>

            <div class="input-group">
                <input type="text" id="role" placeholder="Your Role" required>
                <i class="input-icon fas fa-briefcase"></i>
                <div id="roleError" class="error-message"></div>
            </div>

            <div class="submit-button">
                <button type="submit">Continue to Demo <i class="fas fa-arrow-right"></i></button>
            </div>
            <div class="privacy-note">
                <p>We respect your privacy. Your information is secure and will not be shared.</p>
            </div>
        </form>
    </div>

    <div id="loading-overlay">
        <div id="loading-animation" class="loading-animation"></div>
        <div class="loading-text">Processing...</div>
    </div>

    <script src="landingpage.js"></script>
</body>
</html>
