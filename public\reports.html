<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Assessment Dashboard (Heat Map Version)</title>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
  
  <!-- Matrix Chart plugin (for heat maps) -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-matrix@1.2.0/dist/chartjs-chart-matrix.min.js"></script>

  <!-- Firebase -->
  <script src="https://www.gstatic.com/firebasejs/9.x.x/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.x.x/firebase-firestore-compat.js"></script>
  
  <!-- Initialize Firebase -->
  <script src="firebase-config.js"></script>

  <style>
    /* Import green color palette variables with accessibility improvements */
    :root {
      --soft-green: #A2D6A0;
      --light-mint: #E5F2E5;
      --fresh-green: #8DCE8C;
      --pale-green: #BAE0B7;
      --vibrant-teal-green: #68C692;
      --misty-green: #CEE8CD;
      --primary-green: var(--vibrant-teal-green);
      --secondary-green: var(--soft-green);

      /* Accessible text colors for WCAG 2.1 AA compliance */
      --text-dark: #2D3748;        /* Dark gray for body text on light backgrounds */
      --text-medium: #4A5568;      /* Medium gray for secondary text */
      --text-light: #718096;       /* Light gray for tertiary text */
      --text-on-dark: #FFFFFF;     /* White text for dark green backgrounds */
      --text-on-light: #2D3748;    /* Dark text for light green backgrounds */
      --text-muted: #9CA3AF;       /* Muted text for placeholders and disabled states */
    }

    /* Tabs active state - Updated to green */
    .tab.active {
      border-bottom: 2px solid var(--primary-green);
      color: var(--primary-green);
    }

    /* Chart container */
    .chart-container {
      position: relative;
      width: 100%;
      height: 400px; /* You can adjust height as needed */
      cursor: pointer;
      overflow: hidden; /* Hide scrollbars by default */
      transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    }

    .chart-container.loaded {
      overflow-x: auto; /* Show scrollbars only when chart is loaded */
    }

    .chart-container.transitioning {
      opacity: 0;
      transform: translateY(10px);
    }
    
    .chart-container.transitioned {
      opacity: 1;
      transform: translateY(0);
      animation: fade-in-up 0.4s ease-in-out;
    }

    /* Glass container styles */
    .glass-container {
      background: rgba(255, 255, 255, 0.705);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.18);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      width: 100%;
      max-width: 1024px;
      margin: 0 auto;
      padding: 2rem;
    }

    /* Make charts responsive */
    canvas {
      width: 100% !important;
      height: 100% !important;
      transition: opacity 0.3s ease-in-out;
    }

    /* Title styling - Updated to green */
    .title-major {
      color: var(--primary-green);
    }

    /* Reports card styling */
    .reports-card {
      background-color: rgba(255, 255, 255, 0.925);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      padding: 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      z-index: 10;
      transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 0.5s ease forwards;
    }

    .reports-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    }

    /* Tab content animations */
    .tab-content {
      transition: all 0.5s ease-in-out;
    }

    .tab-content.hidden {
      opacity: 0;
      transform: translateY(20px);
      display: none;
    }

    .tab-content.visible {
      opacity: 1;
      transform: translateY(0);
      display: block;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .reports-card:nth-child(1) { animation-delay: 0.1s; }
    .reports-card:nth-child(2) { animation-delay: 0.2s; }
    .reports-card:nth-child(3) { animation-delay: 0.3s; }
    .reports-card:nth-child(4) { animation-delay: 0.4s; }

    /* Table row animations */
    #usersTableBody tr {
      opacity: 0;
      transform: translateY(10px);
      animation: fadeInUp 0.5s ease forwards;
    }

    #usersTableBody tr:nth-child(1) { animation-delay: 0.1s; }
    #usersTableBody tr:nth-child(2) { animation-delay: 0.2s; }

    /* Smooth scrolling for the table container */
    .max-h-\[600px\] {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    }
    
    .max-h-\[600px\]::-webkit-scrollbar {
      width: 8px;
    }
    
    .max-h-\[600px\]::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .max-h-\[600px\]::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.5);
      border-radius: 4px;
    }
    
    /* Prevent header background from being transparent */
    thead.sticky {
      background-color: rgb(249, 250, 251);
    }

    /* Add these new styles */
    #skillGapChart {
        max-height: 500px;
    }

    .chartjs-tooltip {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #ddd;
        padding: 0.5rem;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Add these tab-specific styles */
    .tab {
      @apply text-gray-500 hover:text-gray-700 px-1 py-4 font-medium text-sm sm:text-base focus:outline-none;
      transition: color 0.2s ease-in-out;
    }

    .tab.active {
      color: var(--primary-green) !important;
      border-bottom: 2px solid var(--primary-green) !important;
    }

    .tab:hover:not(.active) {
      @apply text-gray-700;
    }

    .flex.space-x-8 {
      @apply border-b border-gray-200 mb-8;
    }

    /* Update tab styles to be more specific */
    .tab {
      transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    }

    /* Make active state styles stronger by increasing specificity - Updated to green */
    .tab.active {
      border-bottom: 2px solid var(--primary-green) !important;
      color: var(--primary-green) !important;
    }

    .tab:not(.active) {
      border-bottom: 2px solid transparent;
      color: var(--text-medium);
    }

    .tab:hover:not(.active) {
      color: var(--text-dark);
    }

    /* Skeleton loader animation */
    @keyframes shimmer {
      0% {
        background-position: -1000px 0;
      }
      100% {
        background-position: 1000px 0;
      }
    }

    .skeleton {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 1000px 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
    }

    .chart-skeleton {
      width: 100%;
      height: 100%;
      display: none;
      overflow: hidden; /* Prevent scrolling during skeleton state */
      transition: opacity 0.3s ease-in-out;
      opacity: 0;
    }

    .chart-skeleton.active {
      display: block;
      opacity: 1;
    }

    .chart-canvas.loading {
      display: none;
      opacity: 0;
    }

    /* Add these styles for skill filter tabs */
    [data-filter-tabs] {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 1rem;
    }
    
    .skill-filter-tab {
      transition: color 0.3s ease-in-out, border-color 0.3s ease-in-out, transform 0.2s ease-in-out;
      padding: 0.5rem 0.75rem;
      font-weight: 500;
      font-size: 0.875rem;
      border-bottom: 2px solid transparent;
      position: relative;
    }
    
    .skill-filter-tab.active {
      border-bottom: 2px solid var(--primary-green) !important;
      color: var(--primary-green) !important;
    }
    
    .skill-filter-tab:not(.active) {
      border-bottom: 2px solid transparent;
      color: var(--text-medium);
    }

    .skill-filter-tab:hover:not(.active) {
      color: var(--text-dark);
    }
    
    /* Tab switching animation */
    .skill-filter-tab.tab-switching {
      animation: tab-pulse 0.4s ease-in-out;
    }
    
    @keyframes tab-pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    /* Empty state styling */
    .empty-data-message {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: var(--text-medium);
      font-style: italic;
      text-align: center;
      opacity: 0;
      transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
      padding: 2rem;
      transform: translateY(10px);
    }
    
    .empty-data-message.visible {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Smoother fade in effect */
    @keyframes fade-in-up {
      0% { opacity: 0; transform: translateY(10px); }
      100% { opacity: 1; transform: translateY(0); }
    }

    /* Add styles for the role filter in company overview */
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
      transition: opacity 0.3s ease;
    }

    .filter-badge {
      display: inline-flex;
      align-items: center;
      background-color: var(--light-mint);
      border: 1px solid var(--pale-green);
      color: var(--primary-green);
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      gap: 0.5rem;
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(104, 198, 146, 0.15);
    }

    .filter-badge:hover {
      background-color: var(--pale-green);
      border-color: var(--soft-green);
    }

    .filter-badge .close-icon {
      cursor: pointer;
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: var(--primary-green);
      background-color: var(--light-mint);
      transition: all 0.2s ease;
    }

    .filter-badge .close-icon:hover {
      background-color: var(--pale-green);
      transform: scale(1.1);
    }

    .filter-label {
      color: var(--primary-green);
      font-size: 0.875rem;
      font-weight: 500;
    }

    .reset-filters {
      color: var(--primary-green);
      background: var(--light-mint);
      border: 1px solid var(--pale-green);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(104, 198, 146, 0.15);
    }

    .reset-filters:hover {
      background-color: var(--pale-green);
      border-color: var(--soft-green);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(104, 198, 146, 0.25);
    }

    .fade-enter {
      opacity: 0;
      transform: translateY(10px);
    }

    .fade-enter-active {
      opacity: 1;
      transform: translateY(0);
      transition: opacity 0.3s, transform 0.3s;
    }

    .fade-exit {
      opacity: 1;
    }

    .fade-exit-active {
      opacity: 0;
      transition: opacity 0.3s;
    }

    /* Active filter styles */
    .active-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 0.5rem;
      animation: fadeInUp 0.3s ease-out;
    }
    
    /* Modern dropdown styles with green brand colors */
    select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2368C692'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 0.75rem center;
      background-size: 1em;
      padding-right: 2.5rem;
      border: 1px solid var(--pale-green);
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(104, 198, 146, 0.15);
    }

    select:hover {
      border-color: var(--soft-green);
      box-shadow: 0 2px 4px rgba(104, 198, 146, 0.25);
    }

    select:focus {
      outline: none;
      border-color: var(--primary-green);
      box-shadow: 0 0 0 3px rgba(104, 198, 146, 0.3);
    }
    
    /* Style specifically for role filters */
    #companyRoleFilter,
    #roleFilter {
      font-weight: 500;
      color: var(--text-dark);
      border-radius: 0.375rem;
      background-color: white;
      padding: 0.5rem 2.5rem 0.5rem 0.75rem;
      cursor: pointer;
    }
    
    /* Custom dropdown menu */
    select option {
      font-weight: 400;
      padding: 0.5rem;
    }
    
    /* Search input styling - Updated to green */
    #userSearchInput {
      border: 1px solid var(--pale-green);
      border-radius: 0.375rem;
      padding: 0.5rem 0.75rem;
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(104, 198, 146, 0.15);
    }

    #userSearchInput:hover {
      border-color: var(--soft-green);
    }

    #userSearchInput:focus {
      outline: none;
      border-color: var(--primary-green);
      box-shadow: 0 0 0 3px rgba(104, 198, 146, 0.3);
    }
    
    /* Button styles */
    button {
      transition: all 0.2s ease;
    }
    
    button:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(104, 198, 146, 0.3);
    }

    /* View Profile button styling - Updated to green */
    #usersTableBody button {
      background-color: var(--light-mint);
      padding: 0.375rem 0.75rem;
      border-radius: 0.375rem;
      color: var(--primary-green);
      font-weight: 500;
      transition: all 0.2s ease;
    }

    #usersTableBody button:hover {
      background-color: var(--pale-green);
      transform: translateY(-1px);
    }

    /* Add these new styles for role filter tabs */
    .role-filter-container {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      margin-bottom: 1.5rem;
    }

    .role-filter-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-medium);
    }

    .role-tabs-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      position: relative;
    }

    .role-tab {
      background-color: #f3f4f6;
      color: var(--text-medium);
      border: 1px solid #e5e7eb;
      border-radius: 0.375rem;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;
      user-select: none;
    }

    .role-tab:hover {
      background-color: #e5e7eb;
      transform: translateY(-1px);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .role-tab.selected {
      background-color: var(--light-mint);
      color: var(--primary-green);
      border-color: var(--pale-green);
    }

    .role-tab.selected:hover {
      background-color: var(--pale-green);
    }

    .role-tab .tab-checkmark {
      opacity: 0;
      transform: scale(0.5);
      transition: all 0.2s ease;
      color: var(--primary-green);
      width: 18px;
      height: 18px;
    }

    .role-tab.selected .tab-checkmark {
      opacity: 1;
      transform: scale(1);
    }

    .role-clear-btn {
      font-size: 0.875rem;
      color: var(--text-light);
      background: transparent;
      border: none;
      cursor: pointer;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
    }

    .role-clear-btn:hover {
      color: var(--text-dark);
      background-color: #f3f4f6;
    }

    .role-filter-actions {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 0.5rem;
    }

    .role-filter-badge {
      display: inline-flex;
      align-items: center;
      background-color: var(--light-mint);
      border: 1px solid var(--pale-green);
      color: var(--primary-green);
      padding: 0.375rem 0.75rem;
      border-radius: 1rem;
      font-size: 0.75rem;
      gap: 0.5rem;
      animation: fadeIn 0.3s ease;
    }

    .role-count-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background-color: var(--primary-green);
      color: white;
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 50%;
      font-size: 0.75rem;
      font-weight: 600;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(4px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* For the search field */
    #userSearchInput {
      flex: 1;
    }

    /* Make the filter container in user progress responsive */
    .responsive-filter-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1rem;
    }

    /* For mobile view */
        }

    /* Chart Export Controls Styles */
    .chart-export-controls {
      position: relative;
      z-index: 10;
    }

    .export-dropdown-container {
      position: relative;
      display: inline-block;
    }

    .export-dropdown-btn {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.5rem 0.75rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .export-dropdown-btn:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .export-dropdown-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .export-dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      margin-top: 0.5rem;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      z-index: 50;
      min-width: 140px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
    }

    .export-dropdown-menu.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .export-option {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      width: 100%;
      padding: 0.75rem 1rem;
      background: none;
      border: none;
      text-align: left;
      font-size: 0.875rem;
      color: var(--text-dark);
      cursor: pointer;
      transition: all 0.15s ease;
      border-radius: 0;
    }

    .export-option:first-child {
      border-radius: 0.5rem 0.5rem 0 0;
    }

    .export-option:last-child {
      border-radius: 0 0 0.5rem 0.5rem;
    }

    .export-option:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      color: var(--text-dark);
    }

    .export-option svg {
      color: var(--text-light);
      transition: color 0.15s ease;
    }

    .export-option:hover svg {
      color: var(--primary-green);
    }

    /* Export loading state */
    .export-loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .export-loading .export-dropdown-btn {
      background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    }

    /* Responsive adjustments for export controls */
    @media (max-width: 640px) {
      .export-dropdown-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
      }
      
      .export-dropdown-menu {
        min-width: 120px;
      }

      .export-option {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
      }

      .role-tabs-wrapper {
        max-width: 100%;
        overflow-x: auto;
        padding-bottom: 0.5rem;
        flex-wrap: nowrap;
      }
    }
  </style>
</head>

<body class="p-4 sm:p-6 lg:p-8">
  <div class="glass-container">
    <main class="w-full p-8">

      <!-- Tabs -->
      <div class="mb-6">
        <div class="border-b border-gray-200">
          <nav class="flex -mb-px">
            <button class="tab active px-4 py-2 font-medium text-sm" data-tab="company-overview" style="color: var(--primary-green); border-bottom: 2px solid var(--primary-green);">
              Company Overview
            </button>
            <button class="tab px-4 py-2 font-medium text-sm" data-tab="user-progress" style="color: var(--text-medium);">
              User Progress
            </button>
          </nav>
        </div>
      </div>

      <!-- Company Overview Tab Content -->
      <div id="company-overview" class="tab-content">
        <!-- Replace dropdown with role filter tabs -->
        <div class="role-filter-container">
          <div class="flex items-center justify-between">
            <div class="role-filter-title">Filter by roles:</div>
            <button id="clearRoleFilters" class="role-clear-btn hidden">
              <span>Clear all</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
          
          <div class="role-tabs-wrapper" id="companyRoleTabs">
            <!-- Role tabs will be added dynamically -->
          </div>
          
          <div class="role-filter-actions">
            <div id="activeRoleFilters" class="flex flex-wrap gap-2">
              <!-- Selected role count badge -->
            </div>
            <div id="roleTotalCount" class="text-sm text-gray-500 hidden">
              <!-- Total roles count -->
            </div>
          </div>
        </div>

        <!-- Charts Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Role Distribution Chart -->
          <div class="reports-card">
            <h2 class="title-major text-xl font-semibold mb-2">User Role Distribution</h2>
            <p class="text-sm text-gray-600 mb-4">Distribution of user roles within the company</p>
            <div class="chart-container">
              <div class="chart-skeleton active">
                <!-- Role Distribution Skeleton -->
                <div class="flex items-center justify-center h-full">
                  <div class="w-64 h-64 skeleton rounded-full"></div>
                </div>
              </div>
              <canvas id="roleDistributionChart" class="chart-canvas loading"></canvas>
            </div>
          </div>

          <!-- Completion Chart -->
          <div class="reports-card">
            <h2 class="title-major text-xl font-semibold mb-2">Assessment Completion</h2>
            <p class="text-sm text-gray-600 mb-4">Percentage of users who have completed assessments</p>
            <div class="chart-container">
              <div class="chart-skeleton active">
                <!-- Completion Chart Skeleton -->
                <div class="flex flex-col justify-center h-full space-y-4 p-4">
                  <div class="h-12 skeleton w-3/4"></div>
                  <div class="h-12 skeleton w-1/2"></div>
                  <div class="h-12 skeleton w-2/3"></div>
                </div>
              </div>
              <canvas id="completionChart" class="chart-canvas loading"></canvas>
            </div>
          </div>

          <!-- Skill Gap Heatmap -->
          <div class="reports-card md:col-span-2">
            <div class="flex items-center justify-between mb-2">
              <h2 class="title-major text-xl font-semibold">Skill Gap Heatmap</h2>
              <div class="chart-export-controls">
                <div class="export-dropdown-container">
                  <button class="export-dropdown-btn" id="skillGapExportBtn" 
                          aria-label="Export Skills Gap Heatmap chart" 
                          aria-haspopup="true" 
                          aria-expanded="false"
                          aria-controls="skillGapExportMenu">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    Export
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-3 h-3 ml-1" aria-hidden="true">
                      <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <div class="export-dropdown-menu" id="skillGapExportMenu" role="menu" aria-labelledby="skillGapExportBtn">
                    <button class="export-option" data-format="excel" data-chart="skillGapChart" role="menuitem" aria-label="Export as Excel spreadsheet" tabindex="-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 3v2h2V7H5zm4 0v2h2V7H9zm4 0v2h2V7h-2zM5 11v2h2v-2H5zm4 0v2h2v-2H9zm4 0v2h2v-2h-2z" clip-rule="evenodd" />
                      </svg>
                      Excel Spreadsheet
                    </button>
                    <button class="export-option" data-format="csv" data-chart="skillGapChart" role="menuitem" aria-label="Export as CSV file" tabindex="-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                      CSV Data
                    </button>
                    <button class="export-option" data-format="pdf" data-chart="skillGapChart" role="menuitem" aria-label="Export as PDF document" tabindex="-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 3a1 1 0 000 2h8a1 1 0 100-2H6zm0 3a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                      </svg>
                      PDF Document
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mb-4">Visualizing skill gaps across user roles (Hover over squares)</p>
            <div class="chart-container">
              <div class="chart-skeleton active">
                <!-- Skill Gap Heatmap Skeleton -->
                <div class="grid grid-cols-6 gap-2 p-4 h-full">
                  <!-- Generate 6x6 grid of skeleton boxes -->
                  <script>
                    document.write(Array(36).fill('<div class="skeleton aspect-square"></div>').join(''));
                  </script>
                </div>
              </div>
              <canvas id="skillGapChart" class="chart-canvas loading"></canvas>
            </div>
          </div>

          <!-- Top Recommendations Chart -->
          <div class="reports-card md:col-span-2">
            <div class="flex items-center justify-between mb-2">
              <h2 class="title-major text-xl font-semibold">Top Course Recommendations</h2>
              <div class="chart-export-controls">
                <div class="export-dropdown-container">
                  <button class="export-dropdown-btn" id="recommendationsExportBtn" 
                          aria-label="Export Top Course Recommendations chart" 
                          aria-haspopup="true" 
                          aria-expanded="false"
                          aria-controls="recommendationsExportMenu">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    Export
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-3 h-3 ml-1" aria-hidden="true">
                      <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <div class="export-dropdown-menu" id="recommendationsExportMenu" role="menu" aria-labelledby="recommendationsExportBtn">
                    <button class="export-option" data-format="excel" data-chart="recommendationsChart" role="menuitem" aria-label="Export as Excel spreadsheet" tabindex="-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 3v2h2V7H5zm4 0v2h2V7H9zm4 0v2h2V7h-2zM5 11v2h2v-2H5zm4 0v2h2v-2H9zm4 0v2h2v-2h-2z" clip-rule="evenodd" />
                      </svg>
                      Excel Spreadsheet
                    </button>
                    <button class="export-option" data-format="pdf" data-chart="recommendationsChart" role="menuitem" aria-label="Export as PDF document" tabindex="-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4" aria-hidden="true">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 3a1 1 0 000 2h8a1 1 0 100-2H6zm0 3a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                      </svg>
                      PDF Document
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mb-4">Most recommended courses across the company</p>
            <div class="chart-container h-64">
              <div class="chart-skeleton active">
                <!-- Recommendations Chart Skeleton -->
                <div class="flex items-center justify-center h-full">
                  <div class="w-64 h-64 skeleton rounded-full"></div>
                </div>
              </div>
              <canvas id="recommendationsChart" class="chart-canvas loading"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- User Progress Tab Content -->
      <div id="user-progress" class="tab-content hidden">
        <!-- Replace dropdown with role filter tabs -->
        <div class="responsive-filter-container">
          <input type="text" id="userSearchInput" placeholder="Search users..." class="p-2 border rounded w-64" />
          
          <div class="role-filter-container">
            <div class="role-tabs-wrapper" id="userRoleTabs">
              <!-- User role tabs will be added dynamically -->
            </div>
          </div>
        </div>

        <!-- Users Table with fixed height and scrolling -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="max-h-[600px] overflow-y-auto">
            <table class="min-w-full">
              <thead class="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200" id="usersTableBody">
                <!-- Populated dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Reports JS -->
  <!-- Include User Journey Tracker -->
  <script src="user-journey-tracker.js"></script>
  <script src="reports.js"></script>
</body>
</html>
