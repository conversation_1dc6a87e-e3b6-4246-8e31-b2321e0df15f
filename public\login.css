/* Accessible text colors for WCAG 2.1 AA compliance */
:root {
  --text-dark: #2D3748;        /* Dark gray for body text on light backgrounds */
  --text-medium: #4A5568;      /* Medium gray for secondary text */
  --text-light: #718096;       /* Light gray for tertiary text */
  --text-on-dark: #FFFFFF;     /* White text for dark green backgrounds */
  --text-on-light: #2D3748;    /* Dark text for light green backgrounds */
  --text-muted: #9CA3AF;       /* Muted text for placeholders and disabled states */
}

body {
  background-image: url('ICGlogin.png');
  background-size: cover;
  font-family: 'Montserrat', sans-serif;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  position: relative;
  margin: 0;
  padding: 0;
}

body.dark {
  background-color: #1a202c;
}

/* Rest of the CSS code remains the same */
.login-container {
  width: 100%;
  max-width: 430px;
  padding: 2rem;
  background-color: #ffffffd0;
  border-radius: 1.1rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.363), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  margin: 0 1rem;
}

.login-heading {
  text-align: center;
  margin-bottom: 2rem;
}

.login-heading h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
}

.login-heading p {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-medium);
}

form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.input-group {
  position: relative;
}

.input-group input {
  width: 100%;
  padding: 1rem;
  border: none;
  background-color: #f3f4f6;
  color: var(--text-dark);
  border-radius: 0.75rem;
  outline: none;
  transition: background-color 0.3s ease;
}

.input-group input:focus {
  background-color: #fff;
  border-color: #68C692;
  box-shadow: 0 0 0 3px rgba(104, 198, 146, 0.25);
}

.input-group input::placeholder {
  color: var(--text-muted);
}

input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

input[type="email"]:focus,
input[type="password"]:focus {
  outline: none;
  border-color: #68C692;
}

#emailError,
#passwordError {
  color: #DC2626;
  margin-top: 5px;
  font-size: 13px;
  display: none;
  transition: all 0.3s ease;
}

.friendly-error-container {
  background-color: #FEE2E2;
  border-left: 4px solid #DC2626;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
  color: #991B1B;
  font-size: 0.9rem;
  position: relative;
}

.friendly-error-container p:first-child {
  font-weight: 600;
  margin-bottom: 4px;
}

.friendly-error-container p:last-child {
  font-size: 0.85rem;
  opacity: 0.9;
  margin: 0;
}

input.error {
  border-color: #DC2626;
  background-color: #FEF2F2;
}

@keyframes shake-horizontal {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.input-group input.error:focus {
  border-color: #DC2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.25);
}

#emailError,
#passwordError {
  color: red;
  margin-top: 5px;
  font-size: 13px;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-medium);
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input {
  margin-right: 0.5rem;
  accent-color: #68C692;
}

.forgot-password a {
  color: #6366f1;
  text-decoration: none;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.signin-button button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background-color: #68C692;
  color: #fff;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.signin-button button:hover {
  background-color: #8DCE8C;
}

.request-access {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: var(--text-medium);
}

.request-access a {
  color: #6366f1;
  text-decoration: none;
}

.request-access a:hover {
  text-decoration: underline;
}

@keyframes shake {
  0% { transform: translateX(0); }
  20% { transform: translateX(-10px); }
  40% { transform: translateX(10px); }
  60% { transform: translateX(-10px); }
  80% { transform: translateX(10px); }
  100% { transform: translateX(0); }
}

.animate-shake {
  animation: shake 0.5s;
}

#loading-overlay {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.loading-animation {
  width: 45px; /* Reduced from 80px */
  height: 45px; /* Reduced from 80px */
}

.loading-text {
  color: #fff;
  margin-top: 0.75rem;
  font-size: 0.65rem; /* Reduced size for more minimalistic look */
  letter-spacing: 1px;
  opacity: 0.9;
  font-weight: 400;
  text-align: center;
}

/* Responsive styles for mobile devices */
@media (max-width: 640px) {
  .login-container {
    padding: 1.5rem;
    margin: 0 0.75rem;
    max-width: 100%;
  }

  .login-heading {
    margin-bottom: 1.5rem;
  }

  .login-heading h3 {
    font-size: 1.2rem;
  }

  .login-heading p {
    font-size: 0.8rem;
  }

  form {
    gap: 1rem;
  }

  .input-group input {
    padding: 0.875rem;
    font-size: 0.9rem;
  }

  .remember-forgot {
    font-size: 0.8rem;
  }

  .signin-button button {
    padding: 0.7rem 1.25rem;
  }

  .request-access {
    margin-top: 1.25rem;
    font-size: 0.8rem;
  }
}

/* For very small screens */
@media (max-width: 380px) {
  .login-container {
    padding: 1.25rem;
    margin: 0 0.5rem;
  }

  .login-heading h3 {
    font-size: 1.1rem;
  }

  .login-heading p {
    font-size: 0.75rem;
  }
}

/* Logo footer styling */
#logo-footer {
  transition: all 0.3s ease;
  z-index: 5;
  pointer-events: none;
}

#logo-footer .logo-img {
  max-width: 120px;
  width: auto;
  height: auto;
  opacity: 0.9;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

/* Responsive logo sizing */
@media (max-width: 640px) {
  #logo-footer .logo-img {
    max-width: 100px;
  }
}

@media (max-width: 480px) {
  #logo-footer .logo-img {
    max-width: 80px;
    opacity: 0.7;
  }
}

@media (max-width: 380px) {
  #logo-footer {
    display: none;
  }
}