/**
 * Subscription Check Utility
 * Provides functions to check if a user has an active subscription
 */

(function(global) {
  'use strict';

  // Cache for subscription status to avoid repeated database queries
  let subscriptionStatusCache = null;
  let lastCheckTime = 0;
  const CACHE_DURATION = 60000; // 1 minute cache duration

  /**
   * Checks if the current user has an active subscription
   * @returns {Promise<boolean>} - Always returns true (app is now free)
   */
  async function checkSubscriptionAccess() {
    console.log('Subscription check bypassed - app is now free');
    return true; // Always return true - app is now free
  }

  /**
   * Clears the subscription status cache
   */
  function clearSubscriptionCache() {
    subscriptionStatusCache = null;
    lastCheckTime = 0;
    console.log('Subscription status cache cleared');
  }

  /**
   * Shows the feature access modal if the user doesn't have an active subscription
   * @param {string} featureName - The name of the feature being accessed
   * @returns {Promise<boolean>} - Always returns true (app is now free)
   */
  async function checkFeatureAccess(featureName) {
    console.log(`Feature access check bypassed for ${featureName} - app is now free`);
    return true; // Always return true - app is now free
  }

  // Public API
  global.SubscriptionCheck = {
    checkAccess: checkSubscriptionAccess,
    checkFeatureAccess: checkFeatureAccess,
    clearCache: clearSubscriptionCache
  };
})(typeof window !== 'undefined' ? window : global);
