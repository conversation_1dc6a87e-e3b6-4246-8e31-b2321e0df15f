// invite.js - Link-based Assessment System

// Static assessment links
const assessmentLinks = {
    digital: 'https://icg-digitalskills.onrender.com/SGA.html',
    ai: 'https://icg-aiskills.onrender.com/SGA_ai.html',
    soft: 'https://icg-softskills.onrender.com/SGA.html'
};

/**
 * Initialize the link-based invitation system
 */
async function initializeInviteUsers(company) {
    console.log('Initializing link-based invitation system');

    try {
        // Set static assessment links
        setStaticAssessmentLinks();

        // Set up copy button event listeners
        setupCopyButtonListeners();

        console.log('Link-based invitation system initialized successfully');
    } catch (error) {
        console.error('Error initializing link-based invitation system:', error);
        showNotification('Error initializing assessment links', 'error');
    }
}

/**
 * Set static assessment links
 */
function setStaticAssessmentLinks() {
    // Set static links directly
    document.getElementById('digitalSkillsLink').value = assessmentLinks.digital;
    document.getElementById('aiSkillsLink').value = assessmentLinks.ai;
    document.getElementById('softSkillsLink').value = assessmentLinks.soft;

    console.log('Static assessment links set');
}

/**
 * Set up copy button event listeners
 */
function setupCopyButtonListeners() {
    const copyButtons = document.querySelectorAll('[data-link-type]');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', handleCopyLink);
    });
}

/**
 * Handle copy link button clicks
 */
async function handleCopyLink(event) {
    const button = event.currentTarget;
    const linkType = button.getAttribute('data-link-type');
    const inputId = `${linkType}SkillsLink`;
    const input = document.getElementById(inputId);
    
    if (!input || !input.value) {
        showNotification('Link not available', 'error');
        return;
    }
    
    try {
        // Copy to clipboard
        await navigator.clipboard.writeText(input.value);
        
        // Update button appearance
        updateCopyButtonState(button, true);
        
        // Track the copy action
        await trackLinkCopy(linkType);
        
        showNotification(`${linkType.charAt(0).toUpperCase() + linkType.slice(1)} Skills link copied!`, 'success');
        
        // Reset button after 2 seconds
        setTimeout(() => {
            updateCopyButtonState(button, false);
        }, 2000);
        
    } catch (error) {
        console.error('Error copying link:', error);
        showNotification('Failed to copy link', 'error');
    }
}

/**
 * Update copy button visual state
 */
function updateCopyButtonState(button, copied) {
    const svg = button.querySelector('svg');
    
    if (copied) {
        button.classList.add('copied');
        svg.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M5 13l4 4L19 7" />
        `;
    } else {
        button.classList.remove('copied');
        svg.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        `;
    }
}

/**
 * Track link copy action for user journey analytics
 */
async function trackLinkCopy(linkType) {
    try {
        if (window.UserJourneyTracker) {
            // Track the link copy as "used" interaction
            await window.UserJourneyTracker.trackLinkCopied(linkType, {
                timestamp: new Date().toISOString()
            });
        }
    } catch (error) {
        console.error('Error tracking link copy:', error);
    }
}



/**
 * Show notification to user
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create new notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Export functions for global access
window.initializeInviteUsers = initializeInviteUsers;
