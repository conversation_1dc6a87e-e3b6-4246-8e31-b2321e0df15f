<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referral System Domain Migration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .url-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .legacy {
            background-color: #fff3cd;
        }
        .current {
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Referral System Domain Migration Test</h1>
        
        <div class="test-section">
            <h3>📋 Migration Overview</h3>
            <p>This page tests the referral system migration from <code>dashboard.skillsassess.ai</code> to <code>icg-dashboard.onrender.com</code></p>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Legacy Domain</th>
                        <th>New Domain</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Dashboard</td>
                        <td class="legacy">dashboard.skillsassess.ai</td>
                        <td class="current">icg-dashboard.onrender.com</td>
                        <td id="dashboard-status">⏳ Testing...</td>
                    </tr>
                    <tr>
                        <td>Signup Page</td>
                        <td class="legacy">dashboard.skillsassess.ai/signup.html</td>
                        <td class="current">icg-dashboard.onrender.com/signup.html</td>
                        <td id="signup-status">⏳ Testing...</td>
                    </tr>
                    <tr>
                        <td>Admin Email</td>
                        <td class="legacy"><EMAIL></td>
                        <td class="current"><EMAIL></td>
                        <td id="admin-status">⏳ Testing...</td>
                    </tr>
                    <tr>
                        <td>Terms & Privacy</td>
                        <td class="legacy">skillsassess.ai</td>
                        <td class="current">icg.co.uk</td>
                        <td id="terms-status">⏳ Testing...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🔗 Referral Link Generation Test</h3>
            <p>Test the generation of referral links with the new domain configuration.</p>
            
            <button onclick="testReferralLinkGeneration()">Test Referral Link Generation</button>
            <div id="referral-test-results"></div>
        </div>

        <div class="test-section">
            <h3>🌐 Domain Configuration Test</h3>
            <p>Verify that the domain configuration is properly loaded and accessible.</p>
            
            <button onclick="testDomainConfiguration()">Test Domain Configuration</button>
            <div id="domain-config-results"></div>
        </div>

        <div class="test-section">
            <h3>📧 Email Configuration Test</h3>
            <p>Test email recipient configuration for notifications.</p>
            
            <button onclick="testEmailConfiguration()">Test Email Configuration</button>
            <div id="email-config-results"></div>
        </div>

        <div class="test-section">
            <h3>🔄 Backward Compatibility Test</h3>
            <p>Verify that legacy domain detection and handling works correctly.</p>
            
            <button onclick="testBackwardCompatibility()">Test Backward Compatibility</button>
            <div id="backward-compat-results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Test Results Summary</h3>
            <div id="test-summary">
                <p>Click the test buttons above to run comprehensive tests of the referral system migration.</p>
            </div>
        </div>
    </div>

    <!-- Load domain configuration -->
    <script src="domain-config.js"></script>
    
    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
            
            testResults.total++;
            if (type === 'success') testResults.passed++;
            if (type === 'error') testResults.failed++;
            
            updateTestSummary();
        }

        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            const passRate = testResults.total > 0 ? (testResults.passed / testResults.total * 100).toFixed(1) : 0;
            
            summary.innerHTML = `
                <div class="test-result ${testResults.failed === 0 ? 'success' : 'info'}">
                    <strong>Test Summary:</strong> ${testResults.passed}/${testResults.total} tests passed (${passRate}%)
                    ${testResults.failed > 0 ? `<br><strong>Failed:</strong> ${testResults.failed} tests` : ''}
                </div>
            `;
        }

        function testReferralLinkGeneration() {
            const container = document.getElementById('referral-test-results');
            container.innerHTML = '';
            
            const testCode = 'TEST1234';
            
            try {
                // Test domain configuration availability
                if (typeof window.DomainUtils === 'undefined') {
                    addTestResult('referral-test-results', '❌ Domain configuration not loaded', 'error');
                    return;
                }
                
                // Test referral link generation
                const referralLink = window.DomainUtils.getReferralLink(testCode);
                const expectedDomain = 'icg-dashboard.onrender.com';
                
                if (referralLink.includes(expectedDomain)) {
                    addTestResult('referral-test-results', `✅ Referral link uses correct domain: ${referralLink}`, 'success');
                    document.getElementById('signup-status').textContent = '✅ Updated';
                } else {
                    addTestResult('referral-test-results', `❌ Referral link uses wrong domain: ${referralLink}`, 'error');
                    document.getElementById('signup-status').textContent = '❌ Failed';
                }
                
                // Test signup URL
                const signupUrl = window.DomainUtils.getDashboardUrl() + '/signup.html';
                if (signupUrl.includes(expectedDomain)) {
                    addTestResult('referral-test-results', `✅ Signup URL correct: ${signupUrl}`, 'success');
                    document.getElementById('dashboard-status').textContent = '✅ Updated';
                } else {
                    addTestResult('referral-test-results', `❌ Signup URL incorrect: ${signupUrl}`, 'error');
                    document.getElementById('dashboard-status').textContent = '❌ Failed';
                }
                
            } catch (error) {
                addTestResult('referral-test-results', `❌ Error testing referral links: ${error.message}`, 'error');
            }
        }

        function testDomainConfiguration() {
            const container = document.getElementById('domain-config-results');
            container.innerHTML = '';
            
            try {
                if (typeof window.DOMAIN_CONFIG === 'undefined') {
                    addTestResult('domain-config-results', '❌ DOMAIN_CONFIG not available', 'error');
                    return;
                }
                
                const config = window.DOMAIN_CONFIG;
                
                // Test current domain configuration
                if (config.current.dashboard === 'https://icg-dashboard.onrender.com') {
                    addTestResult('domain-config-results', '✅ Current dashboard domain configured correctly', 'success');
                } else {
                    addTestResult('domain-config-results', `❌ Current dashboard domain incorrect: ${config.current.dashboard}`, 'error');
                }
                
                // Test legacy domain configuration
                if (config.legacy.dashboard === 'https://dashboard.skillsassess.ai') {
                    addTestResult('domain-config-results', '✅ Legacy dashboard domain preserved for compatibility', 'success');
                } else {
                    addTestResult('domain-config-results', '❌ Legacy dashboard domain not preserved', 'error');
                }
                
                // Test assessment domains
                const assessmentDomains = ['digital', 'ai', 'soft'];
                assessmentDomains.forEach(type => {
                    const url = config.assessments[type];
                    if (url && url.includes('icg-')) {
                        addTestResult('domain-config-results', `✅ ${type} assessment domain configured: ${url}`, 'success');
                    } else {
                        addTestResult('domain-config-results', `❌ ${type} assessment domain missing or incorrect`, 'error');
                    }
                });
                
            } catch (error) {
                addTestResult('domain-config-results', `❌ Error testing domain configuration: ${error.message}`, 'error');
            }
        }

        function testEmailConfiguration() {
            const container = document.getElementById('email-config-results');
            container.innerHTML = '';
            
            try {
                if (typeof window.DomainUtils === 'undefined') {
                    addTestResult('email-config-results', '❌ DomainUtils not available', 'error');
                    return;
                }
                
                // Test admin email
                const adminEmail = window.DomainUtils.getAdminEmail();
                if (adminEmail === '<EMAIL>') {
                    addTestResult('email-config-results', `✅ Admin email updated: ${adminEmail}`, 'success');
                    document.getElementById('admin-status').textContent = '✅ Updated';
                } else {
                    addTestResult('email-config-results', `❌ Admin email incorrect: ${adminEmail}`, 'error');
                    document.getElementById('admin-status').textContent = '❌ Failed';
                }
                
                // Test notification recipients
                const recipients = window.DomainUtils.getNotificationRecipients();
                const expectedDomain = 'icg.co.uk';
                const allCorrect = recipients.every(email => email.includes(expectedDomain));
                
                if (allCorrect) {
                    addTestResult('email-config-results', `✅ All notification recipients use new domain (${recipients.length} recipients)`, 'success');
                } else {
                    addTestResult('email-config-results', `❌ Some notification recipients use old domain`, 'error');
                }
                
                // Test terms and privacy URLs
                const termsUrl = window.DomainUtils.getTermsUrl();
                const privacyUrl = window.DomainUtils.getPrivacyUrl();
                
                if (termsUrl.includes('icg.co.uk') && privacyUrl.includes('icg.co.uk')) {
                    addTestResult('email-config-results', '✅ Terms and Privacy URLs updated to icg.co.uk', 'success');
                    document.getElementById('terms-status').textContent = '✅ Updated';
                } else {
                    addTestResult('email-config-results', '❌ Terms and Privacy URLs not updated', 'error');
                    document.getElementById('terms-status').textContent = '❌ Failed';
                }
                
            } catch (error) {
                addTestResult('email-config-results', `❌ Error testing email configuration: ${error.message}`, 'error');
            }
        }

        function testBackwardCompatibility() {
            const container = document.getElementById('backward-compat-results');
            container.innerHTML = '';
            
            try {
                if (typeof window.DomainUtils === 'undefined') {
                    addTestResult('backward-compat-results', '❌ DomainUtils not available', 'error');
                    return;
                }
                
                // Test legacy domain detection
                const legacyDomains = [
                    'dashboard.skillsassess.ai',
                    'skillsassess.ai',
                    'https://dashboard.skillsassess.ai/signup.html'
                ];
                
                legacyDomains.forEach(domain => {
                    const isLegacy = window.DomainUtils.isLegacyDomain(domain);
                    if (isLegacy) {
                        addTestResult('backward-compat-results', `✅ Legacy domain detected: ${domain}`, 'success');
                    } else {
                        addTestResult('backward-compat-results', `❌ Legacy domain not detected: ${domain}`, 'error');
                    }
                });
                
                // Test current domain is not detected as legacy
                const currentDomains = [
                    'icg-dashboard.onrender.com',
                    'icg.co.uk'
                ];
                
                currentDomains.forEach(domain => {
                    const isLegacy = window.DomainUtils.isLegacyDomain(domain);
                    if (!isLegacy) {
                        addTestResult('backward-compat-results', `✅ Current domain not flagged as legacy: ${domain}`, 'success');
                    } else {
                        addTestResult('backward-compat-results', `❌ Current domain incorrectly flagged as legacy: ${domain}`, 'error');
                    }
                });
                
            } catch (error) {
                addTestResult('backward-compat-results', `❌ Error testing backward compatibility: ${error.message}`, 'error');
            }
        }

        // Run initial status check
        document.addEventListener('DOMContentLoaded', function() {
            // Check if domain configuration is loaded
            if (typeof window.DOMAIN_CONFIG !== 'undefined') {
                console.log('✅ Domain configuration loaded successfully');
            } else {
                console.error('❌ Domain configuration failed to load');
            }
        });
    </script>
</body>
</html>
