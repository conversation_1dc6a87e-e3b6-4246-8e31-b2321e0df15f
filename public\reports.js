// Green color palette constants
const GREEN_COLORS = {
  softGreen: '#A2D6A0',
  lightMint: '#E5F2E5',
  freshGreen: '#8DCE8C',
  paleGreen: '#BAE0B7',
  vibrantTealGreen: '#68C692',
  mistyGreen: '#CEE8CD',
  primary: '#68C692',
  secondary: '#A2D6A0'
};

// Enhanced heat map color palette with better contrast and differentiation
const HEAT_MAP_COLORS = {
  noGap: 'rgba(240,248,240,0.7)',        // Very Light Green - No gap (0)
  minor: 'rgba(200,230,200,0.85)',       // Light Green - Minor (1-3)
  moderate: 'rgba(144,200,144,0.9)',     // Medium Green - Moderate (4-6)
  significant: 'rgba(88,160,88,0.9)',    // Dark Green - Significant (7-9)
  severe: 'rgba(34,120,34,0.95)',        // Very Dark Green - Severe (10-12)
  critical: 'rgba(20,80,20,0.95)'        // Darkest Green - Critical (13+)
};

// Custom plugin for displaying percentage labels directly on pie chart slices
const pieChartLabelsPlugin = {
  id: 'pieChartLabels',
  afterDraw: function(chart) {
    if (chart.config.type !== 'doughnut' && chart.config.type !== 'pie') return;

    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    const centerX = (chartArea.left + chartArea.right) / 2;
    const centerY = (chartArea.top + chartArea.bottom) / 2;

    // Only proceed if we have data
    if (!chart.data.datasets[0].data.length) return;

    // Calculate total value
    const total = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);

    // Draw center text if specified
    if (chart.options.centerText) {
      ctx.save();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Draw main center text - Updated to green
      if (chart.options.centerText.mainText) {
        ctx.font = chart.options.centerText.mainFont || 'bold 24px Montserrat, sans-serif';
        ctx.fillStyle = chart.options.centerText.mainColor || GREEN_COLORS.primary;
        ctx.fillText(chart.options.centerText.mainText, centerX, centerY - 10);
      }

      // Draw sub center text
      if (chart.options.centerText.subText) {
        ctx.font = chart.options.centerText.subFont || '14px Montserrat, sans-serif';
        ctx.fillStyle = chart.options.centerText.subColor || '#6B7280';
        ctx.fillText(chart.options.centerText.subText, centerX, centerY + 15);
      }

      ctx.restore();
    }

    // Draw percentage labels on slices
    chart.data.datasets[0].data.forEach((value, i) => {
      if (value === 0) return;

      // Skip small slices (less than 5%)
      const percentage = ((value / total) * 100).toFixed(0);
      if (percentage < 5) return;

      // Get the meta data for this slice
      const meta = chart.getDatasetMeta(0);
      const arc = meta.data[i];

      // Calculate the angle in the middle of the arc
      const angle = arc.startAngle + (arc.endAngle - arc.startAngle) / 2;

      // Calculate the position for the label
      const radius = chart.innerRadius + (chart.outerRadius - chart.innerRadius) / 2;
      const offsetRadius = chart.outerRadius * 0.65;
      const x = centerX + Math.cos(angle) * offsetRadius;
      const y = centerY + Math.sin(angle) * offsetRadius;

      // Draw the label
      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(angle + Math.PI / 2);
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.font = 'bold 12px Montserrat, sans-serif';
      ctx.fillStyle = '#FFFFFF';

      // Add text shadow for better visibility
      ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
      ctx.shadowBlur = 3;
      ctx.fillText(percentage + '%', 0, 0);
      ctx.restore();
    });
  }
};

const axisLabelHoverPlugin = {
  id: 'axisLabelHover',
  hoveredLabel: null,

  afterUpdate(chart, args, options) {
    if (!chart.$axisHoverState) {
      chart.$axisHoverState = {
        xIndex: null,
        yIndex: null,
      };
    }
  },

  afterDraw(chart, args, options) {
    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    const xAxis = chart.scales.x;
    const yAxis = chart.scales.y;
    if (!xAxis || !yAxis) return;

    const { xIndex, yIndex } = chart.$axisHoverState;

    if (xIndex !== null) {
      const label = xAxis.getLabelForValue(xIndex);
      const xCoord = xAxis.getPixelForTick(xIndex);
      const yCoord = chartArea.bottom + 30;

      ctx.save();
      ctx.font = 'bold 14px sans-serif';
      ctx.fillStyle = '#000';
      ctx.textAlign = 'center';
      ctx.fillText(label, xCoord, yCoord);
      ctx.restore();
    }

    if (yIndex !== null) {
      const label = yAxis.getLabelForValue(yIndex);
      const xCoord = chartArea.left - 10;
      const yCoord = yAxis.getPixelForTick(yIndex) + 5;

      ctx.save();
      ctx.font = 'bold 14px sans-serif';
      ctx.fillStyle = '#000';
      ctx.textAlign = 'right';
      ctx.fillText(label, xCoord, yCoord);
      ctx.restore();
    }
  },

  afterEvent(chart, args, options) {
    const event = args.event;
    if (!event || !chart.scales.x || !chart.scales.y) return;

    const xAxis = chart.scales.x;
    const yAxis = chart.scales.y;
    const xLabels = xAxis.getLabels();
    const yLabels = yAxis.getLabels();

    chart.$axisHoverState = { xIndex: null, yIndex: null };

    const { x, y } = event;
    const xLabelAreaTop = xAxis.bottom;
    const xLabelAreaBottom = xLabelAreaTop + 40;
    const yLabelAreaRight = yAxis.left;
    const yLabelAreaLeft = yLabelAreaRight - 80;

    if (y >= xLabelAreaTop && y <= xLabelAreaBottom) {
      let closestIndex = null;
      let minDist = Infinity;
      xLabels.forEach((label, i) => {
        const tickX = xAxis.getPixelForTick(i);
        const dist = Math.abs(x - tickX);
        if (dist < minDist && dist < 30) {
          minDist = dist;
          closestIndex = i;
        }
      });
      if (closestIndex !== null) {
        chart.$axisHoverState.xIndex = closestIndex;
      }
    }

    if (x <= yLabelAreaRight && x >= yLabelAreaLeft) {
      let closestIndex = null;
      let minDist = Infinity;
      yLabels.forEach((label, i) => {
        const tickY = yAxis.getPixelForTick(i);
        const dist = Math.abs(y - tickY);
        if (dist < minDist && dist < 30) {
          minDist = dist;
          closestIndex = i;
        }
      });
      if (closestIndex !== null) {
        chart.$axisHoverState.yIndex = closestIndex;
      }
    }

    chart.draw();
  },
};

Chart.register(axisLabelHoverPlugin);
Chart.register(pieChartLabelsPlugin);

window.ReportsManager = {
  // Data containers and defaults
  charts: {},
  activeChart: null,
  modal: null,
  userCompany: null,
  fullUserData: [],
  digitalRoleSkillGaps: {},
  softRoleSkillGaps: {},
  aiRoleSkillGaps: {},
  digitalCourseRecommendations: {},
  softCourseRecommendations: {},
  aiCourseRecommendations: {},
  currentSkillGapFilter: 'combined',
  currentRecommendationFilter: 'combined',
  currentRecommendationFilter: 'combined',
  companyData: {
    roleDistribution: {},
    completionStatus: {},
    digitalRoleSkillGaps: {},
    softRoleSkillGaps: {},
    aiRoleSkillGaps: {},
    digitalCourseRecommendations: {},
    softCourseRecommendations: {},
    aiCourseRecommendations: {}
  },
  activeRoleFilters: [],
  originalCompanyData: null,
  userProgressActiveRoles: [],

  /**
   * Initialize ReportsManager.
   */
  async initialize(company = 'company-abc') {
    this.showAllSkeletons();

    if (!Chart.controllers.matrix) {
      throw new Error("Matrix chart plugin not loaded. Please check script imports.");
    }

    // Track reports page access
    trackMilestone('reports_page_viewed', {
      userCompany: company || 'company-abc'
    });

    // Ensure we use the right company name when in demo mode
    if (window.isDemoMode) {
      this.userCompany = 'Barefoot eLearning';
      console.log('Reports page using demo company:', this.userCompany);
    } else {
      this.userCompany = company;
      console.log('Reports page using company:', this.userCompany);
    }

    this.createModal();
    this.initializeCharts();
    await this.fetchAndProcessData();
    this.initializeTabs();
    this.initializeSkillFilterTabs();
    this.initializeCompanyFilters();
    this.setupChartExpansion();
    this.setupExportControls();
    this.hideAllSkeletons();
  },

  /**
   * Fetch data from Firestore, process assessments, and update charts/tables.
   */
  async fetchAndProcessData() {
    try {
      showLoadingOverlay();
      const companyRef = db.collection('companies').doc(this.userCompany);
      const usersSnapshot = await companyRef.collection('users').get();

      const userData = [];
      const roleDistribution = {};
      const completionStatus = { completed: 0, started: 0, aborted: 0 };

      // Reset containers
      this.digitalRoleSkillGaps = {};
      this.softRoleSkillGaps = {};
      this.aiRoleSkillGaps = {};
      this.digitalCourseRecommendations = {};
      this.softCourseRecommendations = {};
      this.aiCourseRecommendations = {};

      // These combined objects will hold merged data for the "combined" view.
      const combinedRoleSkillGaps = {};
      const combinedCourseRecommendations = {};

      await Promise.all(
        usersSnapshot.docs.map(async (doc) => {
          const user = doc.data();
          const email = doc.id;

          // Tally role distribution
          roleDistribution[user.userRole] = (roleDistribution[user.userRole] || 0) + 1;
          // Tally completion status
          const status = user.status.toLowerCase();
          if (completionStatus.hasOwnProperty(status)) {
            completionStatus[status]++;
          }

          // Process all three assessments using the generic helper below.
          const digitalData = await this.processAssessment(
            user,
            doc.ref,
            user.lastAssessmentId,
            'assessmentResults',
            this.digitalCourseRecommendations,
            combinedRoleSkillGaps,
            combinedCourseRecommendations,
            this.digitalRoleSkillGaps
          );
          const softData = await this.processAssessment(
            user,
            doc.ref,
            user.lastSoftSkillsAssessmentId,
            'softSkillsAssessmentResults',
            this.softCourseRecommendations,
            combinedRoleSkillGaps,
            combinedCourseRecommendations,
            this.softRoleSkillGaps
          );
          const aiData = await this.processAssessment(
            user,
            doc.ref,
            user.lastAssessmentId_ai,
            'assessmentResults_ai',
            this.aiCourseRecommendations,
            combinedRoleSkillGaps,
            combinedCourseRecommendations,
            this.aiRoleSkillGaps
          );

          // Determine latest assessment timestamp
          const lastAssessmentTimestamp = this.getLatestAssessmentTimestamp(digitalData, softData, aiData);

          // Build user data for the table
          userData.push({
            name: `${user.firstName} ${user.lastName}`,
            email: user.userEmail,
            role: user.userRole,
            status: user.status,
            lastAssessmentDate: lastAssessmentTimestamp,
            lastAssessmentId: user.lastAssessmentId || user.lastSoftSkillsAssessmentId || user.lastAssessmentId_ai,
            timestamp: lastAssessmentTimestamp
              ? lastAssessmentTimestamp.getTime()
              : user.lastUpdated
              ? user.lastUpdated.toDate().getTime()
              : user.createdAt
              ? user.createdAt.toDate().getTime()
              : 0,
            hasDigitalSkills: !!digitalData,
            hasSoftSkills: !!softData,
            hasAISkills: !!aiData
          });
        })
      );

      // Store and backup company data.
      this.companyData = {
        roleDistribution,
        completionStatus,
        digitalRoleSkillGaps: this.digitalRoleSkillGaps,
        softRoleSkillGaps: this.softRoleSkillGaps,
        aiRoleSkillGaps: this.aiRoleSkillGaps,
        digitalCourseRecommendations: this.digitalCourseRecommendations,
        softCourseRecommendations: this.softCourseRecommendations,
        aiCourseRecommendations: this.aiCourseRecommendations
      };
      this.originalCompanyData = JSON.parse(JSON.stringify(this.companyData));

      // Update UI charts and filters.
      this.updateRoleFilterOptions(Object.keys(roleDistribution));
      this.updateRoleDistributionChart(roleDistribution);
      this.updateCompletionChart(completionStatus);
      this.updateSkillGapChartFiltered();
      this.updateRecommendationsChartFiltered();
      this.populateUsersTable(userData);
    } catch (error) {
      console.error('Error fetching report data:', error);
      showNotification('Error loading report data', 'error');
    } finally {
      hideLoadingOverlay();
    }
  },

  /**
   * Generic helper to process an assessment.
   * Uses the provided parameters to update both the local (digital/soft) and combined containers.
   */
  async processAssessment(
    user,
    userRef,
    assessmentId,
    collectionName,
    localRecContainer,
    combinedRoleSkillGaps,
    combinedCourseRecommendations,
    localSkillGapsContainer
  ) {
    try {
      if (!assessmentId) return null;
      const assessmentDoc = await userRef.collection(collectionName).doc(assessmentId).get();
      if (!assessmentDoc.exists) return null;
      const assessmentData = assessmentDoc.data();
      const role = user.userRole;

      if (assessmentData.courseRecommendations) {
        assessmentData.courseRecommendations.forEach((rec) => {
          const skill = rec.courseName || rec.course;
          localRecContainer[skill] = (localRecContainer[skill] || 0) + 1;
          combinedCourseRecommendations[skill] = (combinedCourseRecommendations[skill] || 0) + 1;

          if (!localSkillGapsContainer[role]) localSkillGapsContainer[role] = {};
          localSkillGapsContainer[role][skill] = (localSkillGapsContainer[role][skill] || 0) + 1;

          if (!combinedRoleSkillGaps[role]) combinedRoleSkillGaps[role] = {};
          combinedRoleSkillGaps[role][skill] = (combinedRoleSkillGaps[role][skill] || 0) + 1;
        });
      }
      return {
        timestamp: assessmentData.timestamp ? assessmentData.timestamp.toDate() : null,
        data: assessmentData
      };
    } catch (error) {
      console.error(`Error processing ${collectionName}:`, error);
      return null;
    }
  },

  /**
   * Returns the most recent timestamp from the three assessments.
   */
  getLatestAssessmentTimestamp(digitalData, softData, aiData) {
    const digitalTimestamp = digitalData?.timestamp || null;
    const softTimestamp = softData?.timestamp || null;
    const aiTimestamp = aiData?.timestamp || null;

    const timestamps = [digitalTimestamp, softTimestamp, aiTimestamp].filter(Boolean);
    if (timestamps.length === 0) return null;

    return timestamps.reduce((latest, current) =>
      latest > current ? latest : current
    );
  },

  // ------------------------------
  // Filtering Utility Methods
  // ------------------------------
  filterCompletionStatusByRoles(roles) {
    const filteredStatus = { completed: 0, started: 0, aborted: 0 };
    if (!this.fullUserData) return filteredStatus;
    this.fullUserData.forEach((user) => {
      if (roles.includes(user.role)) {
        const status = user.status.toLowerCase();
        if (filteredStatus.hasOwnProperty(status)) filteredStatus[status]++;
      }
    });
    return filteredStatus;
  },
  filterSkillGapsByRoles(skillGaps, roles) {
    const filtered = {};
    roles.forEach((role) => {
      if (skillGaps[role]) {
        filtered[role] = { ...skillGaps[role] };
      }
    });
    return filtered;
  },
  filterCourseRecommendationsByRoles(roles, skillType) {
    const filtered = {};
    const skillGaps =
      skillType === 'digital'
        ? this.originalCompanyData.digitalRoleSkillGaps
        : skillType === 'soft'
        ? this.originalCompanyData.softRoleSkillGaps
        : this.originalCompanyData.aiRoleSkillGaps;
    roles.forEach((role) => {
      if (skillGaps[role]) {
        Object.entries(skillGaps[role]).forEach(([skill, count]) => {
          filtered[skill] = (filtered[skill] || 0) + count;
        });
      }
    });
    return filtered;
  },

  // ------------------------------
  // UI / Chart Update Methods
  // ------------------------------
  updateRoleFilterOptions(availableRoles) {
    const sortedRoles = availableRoles.sort((a, b) => a.localeCompare(b));
    // Company overview tabs
    this.createRoleTabs("companyRoleTabs", sortedRoles, (selectedRoles) => {
      this.activeRoleFilters = selectedRoles;
      this.applyRoleFilters(selectedRoles);
      this.updateRoleFilterIndicators(selectedRoles);
    });
    // User progress tabs
    this.createRoleTabs("userRoleTabs", sortedRoles, (selectedRoles) => {
      this.userProgressActiveRoles = selectedRoles;
      this.filterAndDisplayUsers();
    });
  },
  createRoleTabs(containerId, roles, onSelectionChange) {
    const container = document.getElementById(containerId);
    if (!container) return;
    container.innerHTML = "";
    const allRolesTab = this.createRoleTab("all", "All Roles", true);
    container.appendChild(allRolesTab);
    container.selectedRoles = [];
    roles.forEach((role) => {
      const tab = this.createRoleTab(role, role);
      container.appendChild(tab);
    });
    container.addEventListener("click", (event) => {
      const tab = event.target.closest(".role-tab");
      if (!tab) return;
      const roleValue = tab.dataset.role;
      if (roleValue === "all") {
        container.querySelectorAll(".role-tab").forEach((t) => {
          if (t.dataset.role !== "all") {
            t.classList.remove("selected");
          } else {
            t.classList.add("selected");
          }
        });
        container.selectedRoles = [];
        onSelectionChange([]);
        return;
      }
      const allRolesTab = container.querySelector('.role-tab[data-role="all"]');
      if (allRolesTab) allRolesTab.classList.remove("selected");
      if (tab.classList.contains("selected")) {
        tab.classList.remove("selected");
        const index = container.selectedRoles.indexOf(roleValue);
        if (index > -1) container.selectedRoles.splice(index, 1);
      } else {
        tab.classList.add("selected");
        container.selectedRoles.push(roleValue);
      }
      if (container.selectedRoles.length === 0 && allRolesTab) {
        allRolesTab.classList.add("selected");
      }
      onSelectionChange([...container.selectedRoles]);
    });
  },
  createRoleTab(role, label, isSelected = false) {
    const tab = document.createElement("div");
    tab.className = `role-tab ${isSelected ? "selected" : ""}`;
    tab.dataset.role = role;
    const checkmark = document.createElement("span");
    checkmark.className = "tab-checkmark";
    checkmark.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
    `;
    const labelSpan = document.createElement("span");
    labelSpan.textContent = label;
    tab.appendChild(checkmark);
    tab.appendChild(labelSpan);
    return tab;
  },
  updateRoleFilterIndicators(selectedRoles) {
    const clearButton = document.getElementById("clearRoleFilters");
    const activeFiltersContainer = document.getElementById("activeRoleFilters");
    const totalCountElement = document.getElementById("roleTotalCount");
    if (!clearButton || !activeFiltersContainer || !totalCountElement) return;
    if (selectedRoles.length > 0) {
      clearButton.classList.remove("hidden");
      activeFiltersContainer.innerHTML = `
        <div class="role-filter-badge">
          <span>Selected roles</span>
          <span class="role-count-badge">${selectedRoles.length}</span>
        </div>
      `;
      activeFiltersContainer.classList.remove("hidden");
      const totalRoles = Object.keys(this.originalCompanyData.roleDistribution).length;
      totalCountElement.textContent = `${selectedRoles.length} of ${totalRoles} roles`;
      totalCountElement.classList.remove("hidden");
    } else {
      clearButton.classList.add("hidden");
      activeFiltersContainer.innerHTML = "";
      totalCountElement.classList.add("hidden");
    }
  },
  initializeCompanyFilters() {
    const clearFiltersBtn = document.getElementById("clearRoleFilters");
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener("click", () => {
        this.resetCompanyFilters(true);
      });
    }
  },
  applyRoleFilters(roles) {
    if (!this.originalCompanyData) return;
    this.activeRoleFilters = [...roles];
    if (roles.length === 0) {
      this.resetCompanyFilters();
      return;
    }
    this.showAllSkeletons();
    setTimeout(() => {
      const filteredRoleDistribution = {};
      roles.forEach((role) => {
        if (this.originalCompanyData.roleDistribution[role]) {
          filteredRoleDistribution[role] = this.originalCompanyData.roleDistribution[role];
        }
      });
      const filteredCompletionStatus = this.filterCompletionStatusByRoles(roles);
      const filteredDigitalRoleSkillGaps = this.filterSkillGapsByRoles(
        this.originalCompanyData.digitalRoleSkillGaps,
        roles
      );
      const filteredSoftRoleSkillGaps = this.filterSkillGapsByRoles(
        this.originalCompanyData.softRoleSkillGaps,
        roles
      );
      const filteredAIRoleSkillGaps = this.filterSkillGapsByRoles(
        this.originalCompanyData.aiRoleSkillGaps,
        roles
      );
      const filteredDigitalCourseRecs = this.filterCourseRecommendationsByRoles(roles, "digital");
      const filteredSoftCourseRecs = this.filterCourseRecommendationsByRoles(roles, "soft");
      const filteredAICourseRecs = this.filterCourseRecommendationsByRoles(roles, "ai");
      this.companyData = {
        roleDistribution: filteredRoleDistribution,
        completionStatus: filteredCompletionStatus,
        digitalRoleSkillGaps: filteredDigitalRoleSkillGaps,
        softRoleSkillGaps: filteredSoftRoleSkillGaps,
        aiRoleSkillGaps: filteredAIRoleSkillGaps,
        digitalCourseRecommendations: filteredDigitalCourseRecs,
        softCourseRecommendations: filteredSoftCourseRecs,
        aiCourseRecommendations: filteredAICourseRecs
      };
      this.updateRoleDistributionChart(filteredRoleDistribution);
      this.updateCompletionChart(filteredCompletionStatus);
      this.digitalRoleSkillGaps = filteredDigitalRoleSkillGaps;
      this.softRoleSkillGaps = filteredSoftRoleSkillGaps;
      this.aiRoleSkillGaps = filteredAIRoleSkillGaps;
      this.digitalCourseRecommendations = filteredDigitalCourseRecs;
      this.softCourseRecommendations = filteredSoftCourseRecs;
      this.aiCourseRecommendations = filteredAICourseRecs;
      this.updateSkillGapChartFiltered();
      this.updateRecommendationsChartFiltered();
      this.hideAllSkeletons();
    }, 500);
  },
  resetCompanyFilters(manualClear = false) {
    if (!this.originalCompanyData) return;
    this.activeRoleFilters = [];
    const companyRoleTabs = document.getElementById("companyRoleTabs");
    if (companyRoleTabs) {
      companyRoleTabs.selectedRoles = [];
      const allRolesTab = companyRoleTabs.querySelector('.role-tab[data-role="all"]');
      const roleTabs = companyRoleTabs.querySelectorAll(".role-tab");
      roleTabs.forEach((tab) => {
        if (tab.dataset.role === "all") {
          tab.classList.add("selected");
        } else {
          tab.classList.remove("selected");
        }
      });
    }
    this.updateRoleFilterIndicators([]);
    this.showAllSkeletons();
    setTimeout(() => {
      this.companyData = JSON.parse(JSON.stringify(this.originalCompanyData));
      this.digitalRoleSkillGaps = this.originalCompanyData.digitalRoleSkillGaps;
      this.softRoleSkillGaps = this.originalCompanyData.softRoleSkillGaps;
      this.aiRoleSkillGaps = this.originalCompanyData.aiRoleSkillGaps;
      this.digitalCourseRecommendations = this.originalCompanyData.digitalCourseRecommendations;
      this.softCourseRecommendations = this.originalCompanyData.softCourseRecommendations;
      this.aiCourseRecommendations = this.originalCompanyData.aiCourseRecommendations;
      this.updateRoleDistributionChart(this.originalCompanyData.roleDistribution);
      this.updateCompletionChart(this.originalCompanyData.completionStatus);
      this.updateSkillGapChartFiltered();
      this.updateRecommendationsChartFiltered();
      this.hideAllSkeletons();
      if (manualClear) {
        console.log("Filters have been reset");
      }
    }, 500);
  },
  updateRoleDistributionChart(roleData) {
    this.showChartSkeleton("roleDistributionChart");
    if (!this.charts.roleDistribution) return;
    setTimeout(() => {
      this.charts.roleDistribution.data.labels = Object.keys(roleData);
      this.charts.roleDistribution.data.datasets[0].data = Object.values(roleData);
      this.charts.roleDistribution.update();
      this.hideChartSkeleton("roleDistributionChart");
    }, 300);
  },
  updateCompletionChart(statusData) {
    this.showChartSkeleton("completionChart");
    if (!this.charts.completion) return;
    setTimeout(() => {
      this.charts.completion.data.labels = ["Completed", "In Progress", "Aborted"];
      this.charts.completion.data.datasets[0].data = [
        statusData.completed,
        statusData.started,
        statusData.aborted
      ];
      this.charts.completion.options.plugins.tooltip = {
        callbacks: {
          label: function (context) {
            const value = context.raw || 0;
            const total = Object.values(statusData).reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
            return `${context.label}: ${value} (${percentage}%)`;
          }
        }
      };
      this.charts.completion.update();
      this.hideChartSkeleton("completionChart");
    }, 300);
  },
  updateSkillGapChart(roleSkillData) {
    this.showChartSkeleton("skillGapChart");
    const ctx = document.getElementById("skillGapChart")?.getContext("2d");
    if (ctx && this.charts.skillGap) {
      setTimeout(() => {
        const roles = Object.keys(roleSkillData);
        const skills = [
          ...new Set(
            Object.values(roleSkillData).flatMap((r) => Object.keys(r))
          )
        ].sort();
        const matrixData = [];
        roles.forEach((role) => {
          skills.forEach((skill) => {
            matrixData.push({
              x: role,
              y: skill,
              v: roleSkillData[role][skill] || 0
            });
          });
        });
        let xFontSize = 14,
          xCellSize = 35;
        if (roles.length > 8) {
          xFontSize = 12;
          xCellSize = 30;
        }
        if (roles.length > 12) {
          xFontSize = 10;
          xCellSize = 25;
        }
        if (roles.length > 16) {
          xFontSize = 9;
          xCellSize = 20;
        }
        let yFontSize = 14,
          yCellSize = 35;
        if (skills.length > 8) {
          yFontSize = 12;
          yCellSize = 30;
        }
        if (skills.length > 12) {
          yFontSize = 10;
          yCellSize = 25;
        }
        if (skills.length > 16) {
          yFontSize = 9;
          yCellSize = 20;
        }
        const finalCellSize = Math.min(xCellSize, yCellSize);
        this.charts.skillGap.data.datasets[0].data = matrixData;
        this.charts.skillGap.data.datasets[0].width = finalCellSize;
        this.charts.skillGap.data.datasets[0].height = finalCellSize;
        this.charts.skillGap.options.scales.x.labels = roles;
        this.charts.skillGap.options.scales.y.labels = skills;
        this.charts.skillGap.options.scales.x.ticks = {
          ...this.charts.skillGap.options.scales.x.ticks,
          autoSkip: false,
          font: { size: xFontSize }
        };
        this.charts.skillGap.options.scales.y.ticks = {
          ...this.charts.skillGap.options.scales.y.ticks,
          autoSkip: false,
          font: { size: yFontSize }
        };
        this.charts.skillGap.update();
        this.hideChartSkeleton("skillGapChart");
      }, 300);
    }
  },
  updateRecommendationsChart(recommendationsData) {
    this.showChartSkeleton("recommendationsChart");
    if (!this.charts.recommendations) return;
    setTimeout(() => {
      const sortedEntries = Object.entries(recommendationsData)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 7);
      const labels = sortedEntries.map(([course]) => course);
      const values = sortedEntries.map(([, count]) => count);
      this.charts.recommendations.data.labels = labels;
      this.charts.recommendations.data.datasets[0].data = values;
      this.charts.recommendations.options.plugins.legend.labels.font.size = 12;
      this.charts.recommendations.update();
      this.hideChartSkeleton("recommendationsChart");
    }, 300);
  },

  // ------------------------------
  // Modal & Chart Expansion Methods
  // ------------------------------
  createModal() {
    const existingModal = document.getElementById("chartModal");
    if (existingModal) existingModal.remove();
    this.modal = document.createElement("div");
    this.modal.id = "chartModal";
    this.modal.className = "fixed inset-0 bg-black bg-opacity-50 z-50 hidden";
    this.modal.style.display = "none";
    const modalContent = document.createElement("div");
    modalContent.className =
      "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-6 w-4/5 h-4/5";
    const closeButton = document.createElement("button");
    closeButton.id = "closeModal";
    closeButton.className =
      "absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl";
    closeButton.innerHTML = "&times;";
    const canvasContainer = document.createElement("div");
    canvasContainer.className = "w-full h-full";
    const canvas = document.createElement("canvas");
    canvas.id = "expandedChart";
    canvasContainer.appendChild(canvas);
    modalContent.appendChild(closeButton);
    modalContent.appendChild(canvasContainer);
    this.modal.appendChild(modalContent);
    document.body.appendChild(this.modal);
    closeButton.addEventListener("click", () => this.closeModal());
    this.modal.addEventListener("click", (e) => {
      if (e.target === this.modal) this.closeModal();
    });
  },
  closeModal() {
    if (this.modal) {
      this.modal.style.display = "none";
      if (this.activeChart) {
        this.activeChart.destroy();
        this.activeChart = null;
      }
    }
  },

  /**
   * Initialize all charts with common configuration.
   */
  initializeCharts() {
    // Create gradient colors for charts
    const createGradient = (ctx, colorStart, colorEnd) => {
      const gradient = ctx.createLinearGradient(0, 0, 0, 400);
      gradient.addColorStop(0, colorStart);
      gradient.addColorStop(1, colorEnd);
      return gradient;
    };

    // Define gradient colors based on specifications
    const createChartGradients = (ctx) => {
      return {
        purpleViolet: createGradient(ctx, '#5A5CDB', '#8E5BDB'),
        navyBlue: createGradient(ctx, '#1A2B5F', '#324577'),
        tealGreen: createGradient(ctx, '#00AA90', '#30D978'),
        orangeCoral: createGradient(ctx, '#FF8F56', '#FFB347'),
        // Additional gradient colors
        redPink: createGradient(ctx, '#FF5252', '#FF7EB3'),
        blueCyan: createGradient(ctx, '#2196F3', '#00BCD4'),
        greenLime: createGradient(ctx, '#4CAF50', '#CDDC39'),
        purplePink: createGradient(ctx, '#9C27B0', '#E91E63'),
        amberBrown: createGradient(ctx, '#FFC107', '#795548'),
        indigoBlue: createGradient(ctx, '#3F51B5', '#2196F3'),
        deepOrangePeach: createGradient(ctx, '#FF5722', '#FFAB91')
      };
    };

    const overviewOptions = {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000 },
      plugins: { tooltip: { enabled: false }, legend: { display: true, onClick: null } },
      interaction: { mode: "none" }
    };

    // Role Distribution (Doughnut)
    const roleCtx = document.getElementById("roleDistributionChart")?.getContext("2d");
    if (roleCtx) {
      // Create gradients for this chart
      const gradients = createChartGradients(roleCtx);

      this.charts.roleDistribution = new Chart(roleCtx, {
        type: "doughnut",
        data: {
          labels: [],
          datasets: [
            {
              data: [],
              backgroundColor: [
                gradients.purpleViolet,
                gradients.navyBlue,
                gradients.tealGreen,
                gradients.orangeCoral,
                gradients.redPink,
                gradients.blueCyan,
                gradients.greenLime,
                gradients.purplePink,
                gradients.amberBrown,
                gradients.indigoBlue,
                gradients.deepOrangePeach,
                '#5A5CDB',
                '#1A2B5F',
                '#00AA90',
                '#FF8F56',
                '#FF5252',
                '#2196F3',
                '#4CAF50',
                '#9C27B0',
                '#FFC107',
                '#3F51B5',
                '#FF5722'
              ],
              borderColor: "#FFFFFF",
              borderWidth: 2,
              hoverBorderWidth: 0,
              hoverOffset: 10,
              borderRadius: 6
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: "75%",
          layout: { padding: 20 },
          centerText: {
            mainText: "Roles",
            mainFont: "bold 18px Montserrat, sans-serif",
            mainColor: GREEN_COLORS.primary,
            subText: "Distribution",
            subFont: "14px Montserrat, sans-serif",
            subColor: "#6B7280"
          },
          plugins: {
            legend: {
              position: "right",
              labels: {
                padding: 20,
                usePointStyle: true,
                pointStyle: "circle",
                font: { size: 12, family: "'Montserrat', sans-serif" }
              }
            },
            tooltip: {
              enabled: true,
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              titleColor: "#1a202c",
              bodyColor: "#4a5568",
              titleFont: { size: 14, family: "'Montserrat', sans-serif", weight: "600" },
              bodyFont: { size: 12, family: "'Montserrat', sans-serif" },
              padding: 12,
              boxWidth: 10,
              boxHeight: 10,
              boxPadding: 3,
              usePointStyle: true,
              callbacks: {
                label: (context) => {
                  const label = context.label || "";
                  const value = context.raw || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            },
            pieChartLabels: {
              enabled: true
            }
          },
          animation: { animateRotate: true, animateScale: true, duration: 1000, easing: "easeOutQuart" },
          interaction: { mode: "nearest", intersect: true, includeInvisible: false },
          elements: { arc: { borderWidth: 2, borderColor: "#FFFFFF", hoverBorderColor: "#FFFFFF" } }
        }
      });
    }

    // Completion Chart (Bar)
    const completionCtx = document.getElementById("completionChart")?.getContext("2d");
    if (completionCtx) {
      this.charts.completion = new Chart(completionCtx, {
        type: "bar",
        data: {
          labels: ["Completed", "In Progress", "Aborted"],
          datasets: [
            {
              label: "Users",
              data: [0, 0, 0],
              backgroundColor: [GREEN_COLORS.primary, GREEN_COLORS.freshGreen, GREEN_COLORS.paleGreen]
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              grid: { display: true, drawBorder: false },
              ticks: { precision: 0 }
            },
            x: { grid: { display: false } }
          },
          plugins: {
            tooltip: {
              enabled: true,
              callbacks: {
                label: function (context) {
                  const value = context.parsed.y;
                  const total = context.dataset.data.reduce((sum, current) => sum + current, 0);
                  const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : "0";
                  return `${context.dataset.label}: ${value} (${percentage}%)`;
                }
              }
            },
            legend: { display: false }
          }
        }
      });
    }

    // Skill Gap Chart (Matrix)
    const skillGapCtx = document.getElementById("skillGapChart")?.getContext("2d");
    if (skillGapCtx) {
      this.charts.skillGap = new Chart(skillGapCtx, {
        type: "matrix",
        data: { datasets: [{ label: "Skill Gaps", data: [], backgroundColor: function (ctx) {
          const value = ctx.dataset.data[ctx.dataIndex]?.v || 0;
          if (value === 0) return HEAT_MAP_COLORS.noGap;        // Very Light Green - No gap
          if (value <= 3) return HEAT_MAP_COLORS.minor;         // Light Green - Minor gap
          if (value <= 6) return HEAT_MAP_COLORS.moderate;      // Medium Green - Moderate gap
          if (value <= 9) return HEAT_MAP_COLORS.significant;   // Dark Green - Significant gap
          if (value <= 12) return HEAT_MAP_COLORS.severe;       // Very Dark Green - Severe gap
          return HEAT_MAP_COLORS.critical;                      // Darkest Green - Critical gap
        },
        width: 30, height: 30, borderWidth: 1, borderColor: "rgba(255,255,255,0.2)",
        hoverBackgroundColor: function (ctx) {
          const value = ctx.dataset.data[ctx.dataIndex]?.v || 0;
          if (value === 0) return HEAT_MAP_COLORS.noGap.replace('0.7', '0.9');        // Enhanced hover - No gap
          if (value <= 3) return HEAT_MAP_COLORS.minor.replace('0.85', '1');          // Enhanced hover - Minor gap
          if (value <= 6) return HEAT_MAP_COLORS.moderate.replace('0.9', '1');        // Enhanced hover - Moderate gap
          if (value <= 9) return HEAT_MAP_COLORS.significant.replace('0.9', '1');     // Enhanced hover - Significant gap
          if (value <= 12) return HEAT_MAP_COLORS.severe.replace('0.95', '1');        // Enhanced hover - Severe gap
          return HEAT_MAP_COLORS.critical.replace('0.95', '1');                       // Enhanced hover - Critical gap
        } }] },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            tooltip: {
              callbacks: {
                title: () => "",
                label: (ctx) => {
                  const data = ctx.dataset.data[ctx.dataIndex];
                  const severity =
                    data.v === 0
                      ? "No gap"
                      : data.v <= 3
                      ? "Minor gap"
                      : data.v <= 6
                      ? "Moderate gap"
                      : data.v <= 9
                      ? "Significant gap"
                      : data.v <= 12
                      ? "Severe gap"
                      : "Critical gap";
                  return `${data.x} - ${data.y}: ${data.v} users (${severity})`;
                }
              }
            },
            legend: {
              display: true,
              position: "bottom",
              labels: {
                generateLabels: () => [
                  { text: "No gap (0)", fillStyle: HEAT_MAP_COLORS.noGap },        // Very Light Green
                  { text: "Minor (1-3)", fillStyle: HEAT_MAP_COLORS.minor },       // Light Green
                  { text: "Moderate (4-6)", fillStyle: HEAT_MAP_COLORS.moderate }, // Medium Green
                  { text: "Significant (7-9)", fillStyle: HEAT_MAP_COLORS.significant }, // Dark Green
                  { text: "Severe (10-12)", fillStyle: HEAT_MAP_COLORS.severe },   // Very Dark Green
                  { text: "Critical (13+)", fillStyle: HEAT_MAP_COLORS.critical }  // Darkest Green
                ]
              }
            }
          },
          scales: {
            x: { type: "category", position: "bottom", offset: true, grid: { display: false }, ticks: { font: { size: 11 } } },
            y: { type: "category", position: "left", offset: true, grid: { display: false }, ticks: { font: { size: 11 } } }
          }
        }
      });
    }

    // Recommendations Chart (Doughnut)
    const recommendationsCtx = document.getElementById("recommendationsChart")?.getContext("2d");
    if (recommendationsCtx) {
      // Create gradients for this chart
      const gradients = createChartGradients(recommendationsCtx);

      this.charts.recommendations = new Chart(recommendationsCtx, {
        type: "doughnut",
        data: {
          labels: [],
          datasets: [
            {
              data: [],
              backgroundColor: [
                gradients.tealGreen,
                gradients.orangeCoral,
                gradients.purpleViolet,
                gradients.navyBlue,
                gradients.redPink,
                gradients.blueCyan,
                gradients.greenLime,
                gradients.purplePink,
                gradients.amberBrown,
                gradients.indigoBlue,
                gradients.deepOrangePeach,
                '#00AA90',
                '#FF8F56',
                '#5A5CDB',
                '#1A2B5F',
                '#FF5252',
                '#2196F3',
                '#4CAF50',
                '#9C27B0',
                '#FFC107',
                '#3F51B5',
                '#FF5722'
              ],
              borderColor: "#FFFFFF",
              borderWidth: 2,
              hoverBorderWidth: 0,
              hoverOffset: 10,
              borderRadius: 6
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: "75%",
          layout: { padding: 20 },
          centerText: {
            mainText: "Top",
            mainFont: "bold 18px Montserrat, sans-serif",
            mainColor: GREEN_COLORS.primary,
            subText: "Recommendations",
            subFont: "14px Montserrat, sans-serif",
            subColor: "#6B7280"
          },
          plugins: {
            legend: {
              position: "right",
              labels: {
                padding: 20,
                usePointStyle: true,
                pointStyle: "circle",
                font: { size: 12, family: "'Montserrat', sans-serif" }
              }
            },
            tooltip: {
              enabled: true,
              backgroundColor: "rgba(255,255,255,0.9)",
              titleColor: "#1a202c",
              bodyColor: "#4a5568",
              titleFont: { size: 14, family: "'Montserrat', sans-serif", weight: "600" },
              bodyFont: { size: 12, family: "'Montserrat', sans-serif" },
              padding: 12,
              boxWidth: 10,
              boxHeight: 10,
              boxPadding: 3,
              usePointStyle: true,
              callbacks: {
                label: (context) => {
                  const label = context.label || "";
                  const value = context.raw || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            },
            pieChartLabels: {
              enabled: true
            }
          },
          animation: { animateRotate: true, animateScale: true, duration: 1000, easing: "easeOutQuart" },
          interaction: { mode: "nearest", intersect: true, includeInvisible: false },
          elements: { arc: { borderWidth: 2, borderColor: "#FFFFFF", hoverBorderColor: "#FFFFFF" } }
        }
      });
    }
  },

  // ------------------------------
  // Tab & User Table Methods
  // ------------------------------
  initializeTabs() {
    const tabs = document.querySelectorAll(".tab");
    if (!tabs.length) return;
    tabs.forEach((tab) => {
      tab.addEventListener("click", () => {
        tabs.forEach((t) => {
          t.classList.remove("active");
          t.classList.add("text-gray-500");
          t.style.color = "";
          t.style.borderBottomColor = "";
        });
        tab.classList.add("active");
        tab.classList.remove("text-gray-500");
        tab.style.color = GREEN_COLORS.primary;
        tab.style.borderBottomColor = GREEN_COLORS.primary;
        const contentId = tab.getAttribute("data-tab");
        
        // Track tab switching milestone
        trackMilestone('reports_tab_switched', {
          tabName: contentId,
          userCompany: this.userCompany
        });
        
        this.switchTabContent(contentId);
      });
    });
    const activeTab = document.querySelector(".tab.active");
    if (activeTab) {
      activeTab.classList.remove("text-gray-500");
      activeTab.style.color = GREEN_COLORS.primary;
      activeTab.style.borderBottomColor = GREEN_COLORS.primary;
    }
    const searchInput = document.getElementById("userSearchInput");
    const roleFilter = document.getElementById("roleFilter");
    searchInput?.addEventListener("input", () => this.filterAndDisplayUsers());
    roleFilter?.addEventListener("change", () => this.filterAndDisplayUsers());
  },
  switchTabContent(targetId) {
    const allContents = document.querySelectorAll(".tab-content");
    const targetContent = document.getElementById(targetId);
    allContents.forEach((content) => {
      content.classList.remove("visible");
      content.classList.add("hidden");
      content.querySelectorAll(".reports-card, #usersTableBody tr").forEach((el) => {
        el.style.animation = "none";
        el.offsetHeight;
        el.style.animation = null;
      });
    });
    if (targetContent) {
      setTimeout(() => {
        targetContent.classList.remove("hidden");
        targetContent.classList.add("visible");
        targetContent.querySelectorAll(".reports-card, #usersTableBody tr").forEach((el, index) => {
          el.style.animationDelay = `${index * 0.1}s`;
          el.style.animation = "none";
          el.offsetHeight;
          el.style.animation = null;
        });
        if (targetId === "company-overview") {
          Object.values(this.charts).forEach((chart) => {
            if (chart?.options.animation !== false) chart.update("active");
          });
        }
      }, 300);
    }
  },
  populateUsersTable(userData) {
    const tbody = document.getElementById("usersTableBody");
    if (!tbody) return;
    this.fullUserData = userData;
    this.fullUserData.sort((a, b) => b.timestamp - a.timestamp);
    this.filterAndDisplayUsers();
  },
  filterAndDisplayUsers() {
    if (!this.fullUserData) return;
    const searchInput = document.getElementById("userSearchInput");
    const tbody = document.getElementById("usersTableBody");
    if (!tbody) return;
    const searchTerm = (searchInput?.value || "").toLowerCase();
    const selectedRoles = this.userProgressActiveRoles;
    const filteredUsers = this.fullUserData.filter((user) => {
      const matchesSearch =
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm);
      const matchesRole = selectedRoles.length === 0 || selectedRoles.includes(user.role);
      return matchesSearch && matchesRole;
    });
    tbody.innerHTML = filteredUsers
      .map(
        (user) => `
      <tr class="border-t border-gray-200 hover:bg-gray-50">
        <td class="px-6 py-4">${user.name}</td>
        <td class="px-6 py-4">${user.email}</td>
        <td class="px-6 py-4">${user.role}</td>
        <td class="px-6 py-4">
          <span class="px-2 py-1 rounded-full ${this.getStatusClass(user.status)}">
            ${user.status}
          </span>
        </td>
        <td class="px-6 py-4">
          ${
            user.lastAssessmentId
              ? `<button onclick="ReportsManager.handleViewProfile('${user.email}')" style="color: ${GREEN_COLORS.primary}; background-color: ${GREEN_COLORS.lightMint}; padding: 0.375rem 0.75rem; border-radius: 0.375rem; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='${GREEN_COLORS.paleGreen}'" onmouseout="this.style.backgroundColor='${GREEN_COLORS.lightMint}'">View Profile</button>`
              : `<span class="text-gray-400">Analysis not available</span>`
          }
        </td>
      </tr>
    `
      )
      .join("");
    tbody.querySelectorAll("tr").forEach((row, index) => {
      row.style.animationDelay = `${index * 0.05}s`;
    });
  },
  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case "completed":
        return `bg-green-100 text-green-800`;
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  },

  // ------------------------------
  // Chart Expansion / Modal Methods
  // ------------------------------
  setupChartExpansion() {
    document.querySelectorAll(".reports-card .chart-container").forEach((container) => {
      container.addEventListener("click", () => {
        const canvas = container.querySelector("canvas");
        if (!canvas) return;
        const originalChart = Chart.getChart(canvas);
        if (!originalChart) return;
        if (this.activeChart) {
          this.activeChart.destroy();
        }
        const expandedCanvas = document.getElementById("expandedChart");
        if (!expandedCanvas) return;
        const newData = JSON.parse(JSON.stringify(originalChart.data));
        const chartType = originalChart.config.type;
        const originalOptions = originalChart.options;
        if (chartType === "matrix") {
          const expandedOptions = {
            responsive: true,
            maintainAspectRatio: false,
            events: ["mousemove", "mouseout", "click", "touchstart", "touchmove"],
            animation: { duration: 1000 },
            scales: {
              x: {
                type: "category",
                position: "bottom",
                offset: true,
                grid: { display: false },
                ticks: { font: { size: 12 } },
                labels: originalOptions.scales.x.labels
              },
              y: {
                type: "category",
                position: "left",
                offset: true,
                grid: { display: false },
                ticks: { font: { size: 12 } },
                labels: originalOptions.scales.y.labels
              }
            },
            plugins: {
              tooltip: {
                enabled: true,
                callbacks: {
                  title: () => "",
                  label: (ctx) => {
                    const data = ctx.dataset.data[ctx.dataIndex];
                    const severity =
                      data.v === 0
                        ? "No gap"
                        : data.v <= 3
                        ? "Minor gap"
                        : data.v <= 6
                        ? "Moderate gap"
                        : data.v <= 9
                        ? "Significant gap"
                        : data.v <= 12
                        ? "Severe gap"
                        : "Critical gap";
                    return `${data.x} - ${data.y}: ${data.v} users (${severity})`;
                  }
                }
              },
              legend: {
                display: true,
                position: "bottom",
                labels: {
                  generateLabels: () => [
                    { text: "No gap (0)", fillStyle: HEAT_MAP_COLORS.noGap },        // Very Light Green
                    { text: "Minor (1-3)", fillStyle: HEAT_MAP_COLORS.minor },       // Light Green
                    { text: "Moderate (4-6)", fillStyle: HEAT_MAP_COLORS.moderate }, // Medium Green
                    { text: "Significant (7-9)", fillStyle: HEAT_MAP_COLORS.significant }, // Dark Green
                    { text: "Severe (10-12)", fillStyle: HEAT_MAP_COLORS.severe },   // Very Dark Green
                    { text: "Critical (13+)", fillStyle: HEAT_MAP_COLORS.critical }  // Darkest Green
                  ]
                }
              }
            }
          };
          this.activeChart = new Chart(expandedCanvas.getContext("2d"), {
            type: "matrix",
            data: { datasets: [{ label: "Skill Gaps", data: newData.datasets[0].data, width: 35, height: 35, backgroundColor: function (ctx) {
              const value = ctx.dataset.data[ctx.dataIndex]?.v || 0;
              if (value === 0) return HEAT_MAP_COLORS.noGap;        // Very Light Green - No gap
              if (value <= 3) return HEAT_MAP_COLORS.minor;         // Light Green - Minor gap
              if (value <= 6) return HEAT_MAP_COLORS.moderate;      // Medium Green - Moderate gap
              if (value <= 9) return HEAT_MAP_COLORS.significant;   // Dark Green - Significant gap
              if (value <= 12) return HEAT_MAP_COLORS.severe;       // Very Dark Green - Severe gap
              return HEAT_MAP_COLORS.critical;                      // Darkest Green - Critical gap
            }, borderWidth: 1, borderColor: "rgba(255,255,255,0.2)",
            hoverBackgroundColor: function (ctx) {
              const value = ctx.dataset.data[ctx.dataIndex]?.v || 0;
              if (value === 0) return HEAT_MAP_COLORS.noGap.replace('0.7', '0.9');        // Enhanced hover - No gap
              if (value <= 3) return HEAT_MAP_COLORS.minor.replace('0.85', '1');          // Enhanced hover - Minor gap
              if (value <= 6) return HEAT_MAP_COLORS.moderate.replace('0.9', '1');        // Enhanced hover - Moderate gap
              if (value <= 9) return HEAT_MAP_COLORS.significant.replace('0.9', '1');     // Enhanced hover - Significant gap
              if (value <= 12) return HEAT_MAP_COLORS.severe.replace('0.95', '1');        // Enhanced hover - Severe gap
              return HEAT_MAP_COLORS.critical.replace('0.95', '1');                       // Enhanced hover - Critical gap
            } }] },
            options: expandedOptions
          });
        } else {
          const expandedOptions = {
            responsive: true,
            maintainAspectRatio: false,
            animation: { duration: 1000, easing: "easeInOutQuart" },
            scales:
              chartType === "bar"
                ? {
                    y: {
                      beginAtZero: true,
                      grid: { display: true, drawBorder: false },
                      ticks: { precision: 0 }
                    },
                    x: { grid: { display: false } }
                  }
                : undefined,
            plugins: {
              legend: { display: true, position: "bottom", labels: { font: { size: 14 }, padding: 20 } },
              tooltip: {
                enabled: true,
                backgroundColor: "rgba(0,0,0,0.8)",
                titleFont: { size: 16 },
                bodyFont: { size: 14 },
                padding: 12,
                callbacks: {
                  label: function (context) {
                    if (chartType === "bar") {
                      const value = context.parsed.y;
                      const total = context.dataset.data.reduce((sum, current) => sum + current, 0);
                      const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : "0";
                      return `${context.dataset.label}: ${value} (${percentage}%)`;
                    } else {
                      const label = context.label || "";
                      const value = context.parsed || context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = ((value / total) * 100).toFixed(1);
                      return `${label}: ${value} (${percentage}%)`;
                    }
                  }
                }
              },
              pieChartLabels: {
                enabled: chartType === "doughnut" || chartType === "pie"
              }
            }
          };

          // Add center text for doughnut charts in expanded view
          if (chartType === "doughnut") {
            if (canvas.id === "roleDistributionChart") {
              expandedOptions.centerText = {
                mainText: "Roles",
                mainFont: "bold 24px Montserrat, sans-serif",
                mainColor: GREEN_COLORS.primary,
                subText: "Distribution",
                subFont: "16px Montserrat, sans-serif",
                subColor: "#6B7280"
              };
            } else if (canvas.id === "recommendationsChart") {
              expandedOptions.centerText = {
                mainText: "Top",
                mainFont: "bold 24px Montserrat, sans-serif",
                mainColor: GREEN_COLORS.primary,
                subText: "Recommendations",
                subFont: "16px Montserrat, sans-serif",
                subColor: "#6B7280"
              };
            }
          }

          // Create gradients for expanded chart
          const expandedCtx = expandedCanvas.getContext("2d");
          if (chartType === "doughnut") {
            const createGradient = (ctx, colorStart, colorEnd) => {
              const gradient = ctx.createLinearGradient(0, 0, 0, 400);
              gradient.addColorStop(0, colorStart);
              gradient.addColorStop(1, colorEnd);
              return gradient;
            };

            const gradients = {
              purpleViolet: createGradient(expandedCtx, '#5A5CDB', '#8E5BDB'),
              navyBlue: createGradient(expandedCtx, '#1A2B5F', '#324577'),
              tealGreen: createGradient(expandedCtx, '#00AA90', '#30D978'),
              orangeCoral: createGradient(expandedCtx, '#FF8F56', '#FFB347'),
              // Additional gradient colors
              redPink: createGradient(expandedCtx, '#FF5252', '#FF7EB3'),
              blueCyan: createGradient(expandedCtx, '#2196F3', '#00BCD4'),
              greenLime: createGradient(expandedCtx, '#4CAF50', '#CDDC39'),
              purplePink: createGradient(expandedCtx, '#9C27B0', '#E91E63'),
              amberBrown: createGradient(expandedCtx, '#FFC107', '#795548'),
              indigoBlue: createGradient(expandedCtx, '#3F51B5', '#2196F3'),
              deepOrangePeach: createGradient(expandedCtx, '#FF5722', '#FFAB91')
            };

            if (canvas.id === "roleDistributionChart") {
              newData.datasets[0].backgroundColor = [
                gradients.purpleViolet,
                gradients.navyBlue,
                gradients.tealGreen,
                gradients.orangeCoral,
                gradients.redPink,
                gradients.blueCyan,
                gradients.greenLime,
                gradients.purplePink,
                gradients.amberBrown,
                gradients.indigoBlue,
                gradients.deepOrangePeach,
                '#5A5CDB',
                '#1A2B5F',
                '#00AA90',
                '#FF8F56',
                '#FF5252',
                '#2196F3',
                '#4CAF50',
                '#9C27B0',
                '#FFC107',
                '#3F51B5',
                '#FF5722'
              ];
            } else if (canvas.id === "recommendationsChart") {
              newData.datasets[0].backgroundColor = [
                gradients.tealGreen,
                gradients.orangeCoral,
                gradients.purpleViolet,
                gradients.navyBlue,
                gradients.redPink,
                gradients.blueCyan,
                gradients.greenLime,
                gradients.purplePink,
                gradients.amberBrown,
                gradients.indigoBlue,
                gradients.deepOrangePeach,
                '#00AA90',
                '#FF8F56',
                '#5A5CDB',
                '#1A2B5F',
                '#FF5252',
                '#2196F3',
                '#4CAF50',
                '#9C27B0',
                '#FFC107',
                '#3F51B5',
                '#FF5722'
              ];
            }

            // Add rounded corners to expanded chart
            newData.datasets[0].borderRadius = 6;
          }

          this.activeChart = new Chart(expandedCtx, {
            type: chartType,
            data: newData,
            options: expandedOptions
          });
        }
        this.modal.style.display = "flex";
        requestAnimationFrame(() => {
          this.activeChart.resize();
          this.activeChart.update("active");
        });
      });
    });
  },
  cleanupCharts() {
    Object.values(this.charts).forEach((chart) => {
      if (chart) chart.destroy();
    });
    this.charts = {};
    if (this.activeChart) {
      this.activeChart.destroy();
      this.activeChart = null;
    }
    if (this.modal) {
      this.modal.remove();
      this.modal = null;
    }
  },

  // ------------------------------
  // User Profile & Skills Data Methods
  // ------------------------------
  handleViewProfile(email) {
    (async () => {
      try {
        console.log("Starting handleViewProfile for email:", email);
        showLoadingOverlay();
        const combinedSkillsData = await this.fetchUserSkillsData(email);
        await window.showSkillsGapAnalysis(combinedSkillsData);
      } catch (error) {
        console.error("Error in handleViewProfile:", error);
        showNotification("Unable to load user profile", "error");
      } finally {
        hideLoadingOverlay();
      }
    })();
  },
  async fetchUserSkillsData(email) {
    console.log("Fetching user skills data for:", email);
    const userRef = db
      .collection("companies")
      .doc(this.userCompany)
      .collection("users")
      .doc(email);
    try {
      const userDoc = await userRef.get();
      if (!userDoc.exists) throw new Error("User data not found");
      const userData = userDoc.data();
      const digitalSkillsData = await this.fetchDigitalSkillsData(userRef, userData);
      const softSkillsData = await this.fetchSoftSkillsData(userRef, userData);
      const aiSkillsData = await this.fetchAISkillsData(userRef, userData);

      let digitalPath = null;
      let softSkillsPath = null;
      let aiSkillsPath = null;
      if (digitalSkillsData && digitalSkillsData.report) {
        digitalPath = digitalSkillsData.report.learningPath;
        console.log("Digital Skills Learning Path:", digitalPath);
      }
      if (softSkillsData && softSkillsData.report) {
        softSkillsPath = softSkillsData.report.learningPath;
        console.log("Soft Skills Learning Path:", softSkillsPath);
      }
      if (aiSkillsData && aiSkillsData.report) {
        aiSkillsPath = aiSkillsData.report.learningPath;
        console.log("AI Skills Learning Path:", aiSkillsPath);
      }

      const combinedData = {
        metadata: {
          userCompany: this.userCompany,
          userId: email,
          employeeName: `${userData.firstName} ${userData.lastName}`,
          role: userData.userRole,
          isEnrollable:
            !(userData.enrollmentStatus === "enrolled" ||
              userData.enrollmentStatus === "processing"),
          availableAnalysisTypes: [],
          pathsByType: {
            digitalSkills: digitalPath,
            softSkills: softSkillsPath,
            aiSkills: aiSkillsPath
          },
          currentPath: digitalPath || softSkillsPath || aiSkillsPath
        }
      };

      if (
        digitalSkillsData &&
        digitalSkillsData.report &&
        digitalSkillsData.report.competencyAnalysis
      ) {
        digitalSkillsData.report.analysisType = "digitalSkills";
        combinedData.digitalSkills = digitalSkillsData;
        combinedData.metadata.availableAnalysisTypes.push("digitalSkills");
      }
      if (
        softSkillsData &&
        softSkillsData.report &&
        softSkillsData.report.competencyAnalysis
      ) {
        softSkillsData.report.analysisType = "softSkills";
        combinedData.softSkills = softSkillsData;
        combinedData.metadata.availableAnalysisTypes.push("softSkills");
      }
      if (
        aiSkillsData &&
        aiSkillsData.report &&
        aiSkillsData.report.competencyAnalysis
      ) {
        aiSkillsData.report.analysisType = "aiSkills";
        combinedData.aiSkills = aiSkillsData;
        combinedData.metadata.availableAnalysisTypes.push("aiSkills");
      }
      if (combinedData.metadata.availableAnalysisTypes.length === 0) {
        throw new Error("No skills analysis data available for this user");
      }
      console.log("Passing learning paths to skills gap modal:", combinedData.metadata.pathsByType);
      return combinedData;
    } catch (error) {
      console.error("Error in fetchUserSkillsData:", error);
      throw new Error("Failed to load user skills data");
    }
  },
  async fetchDigitalSkillsData(userRef, userData) {
    try {
      const lastAssessmentId = userData.lastAssessmentId;
      if (!lastAssessmentId) return null;
      const summarySnapshot = await userRef
        .collection("assessmentSummaries")
        .orderBy("timestamp", "desc")
        .limit(1)
        .get();
      let currentSection = null;
      let learningPath = null;
      if (!summarySnapshot.empty) {
        const summaryData = summarySnapshot.docs[0].data();
        currentSection = summaryData.currentSection;
        learningPath = this.mapSectionToLearningPath(currentSection);
        console.log("Digital Skills - Current Section:", currentSection, "Mapped Learning Path:", learningPath);
      }
      const assessmentDoc = await userRef.collection("assessmentResults").doc(lastAssessmentId).get();
      if (!assessmentDoc.exists) return null;
      const assessmentData = assessmentDoc.data();
      if (!learningPath) {
        if (assessmentData.section) {
          learningPath = assessmentData.section.toLowerCase();
          console.log("Digital Skills - Fallback to assessmentData.section:", learningPath);
        } else if (userData.currentPath) {
          learningPath = userData.currentPath;
          console.log("Digital Skills - Fallback to userData.currentPath:", learningPath);
        }
      }
      return {
        report: {
          employeeName: `${userData.firstName} ${userData.lastName}`,
          role: userData.userRole,
          learningPath: learningPath,
          currentPath: learningPath,
          competencyAnalysis: assessmentData.competencyAnalysis,
          summary: assessmentData.analysisSummary,
          enrollmentStatus: userData.enrollmentStatus || "not_enrolled",
          email: userData.userEmail
        },
        recommendations:
          assessmentData.courseRecommendations?.map((rec) => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason
          })) || [],
        other_learning_paths_courses:
          assessmentData.otherPathRecommendations?.map((rec) => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason,
            learningPath: rec.learningPath
          })) || []
      };
    } catch (error) {
      console.error("Error fetching digital skills data:", error);
      return null;
    }
  },
  async fetchSoftSkillsData(userRef, userData) {
    try {
      const lastSoftSkillsAssessmentId = userData.lastSoftSkillsAssessmentId;
      if (!lastSoftSkillsAssessmentId) return null;
      const summarySnapshot = await userRef
        .collection("softSkillsSummaries")
        .orderBy("timestamp", "desc")
        .limit(1)
        .get();
      let currentSection = null;
      let learningPath = null;
      if (!summarySnapshot.empty) {
        const summaryData = summarySnapshot.docs[0].data();
        currentSection = summaryData.currentSection;
        learningPath = this.mapSectionToLearningPath(currentSection);
        console.log("Soft Skills - Current Section:", currentSection, "Mapped Learning Path:", learningPath);
      }
      const assessmentDoc = await userRef.collection("softSkillsAssessmentResults").doc(lastSoftSkillsAssessmentId).get();
      if (!assessmentDoc.exists) return null;
      const assessmentData = assessmentDoc.data();
      if (!learningPath) {
        if (assessmentData.metadata?.learningPath) {
          learningPath = assessmentData.metadata.learningPath;
          console.log("Soft Skills - Fallback to assessment metadata:", learningPath);
        } else if (assessmentData.section) {
          learningPath = assessmentData.section.toLowerCase();
          console.log("Soft Skills - Fallback to assessmentData.section:", learningPath);
        } else if (userData.currentPath) {
          learningPath = userData.currentPath;
          console.log("Soft Skills - Fallback to userData.currentPath:", learningPath);
        }
      }
      return {
        report: {
          employeeName: `${userData.firstName} ${userData.lastName}`,
          role: userData.userRole,
          learningPath: learningPath,
          currentPath: learningPath,
          competencyAnalysis: assessmentData.competencyAnalysis,
          summary: assessmentData.analysisSummary,
          enrollmentStatus: userData.enrollmentStatus || "not_enrolled",
          email: userData.userEmail
        },
        recommendations:
          assessmentData.courseRecommendations?.map((rec) => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason
          })) || [],
        other_learning_paths_courses:
          assessmentData.otherPathRecommendations?.map((rec) => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason,
            learningPath: rec.learningPath
          })) || []
      };
    } catch (error) {
      console.error("Error fetching soft skills data:", error);
      return null;
    }
  },
  async fetchAISkillsData(userRef, userData) {
    try {
      const lastAssessmentId_ai = userData.lastAssessmentId_ai;
      if (!lastAssessmentId_ai) return null;

      // Fetch AI assessment summaries
      const summarySnapshot = await userRef
        .collection("assessmentSummaries_ai")
        .orderBy("timestamp", "desc")
        .limit(1)
        .get();

      let learningPath = null;
      if (!summarySnapshot.empty) {
        const summaryData = summarySnapshot.docs[0].data();
        learningPath = summaryData.learningPath || summaryData.currentSection;
        console.log("AI Skills - Learning Path from summary:", learningPath);
      }

      // Fetch AI assessment results
      const assessmentDoc = await userRef.collection("assessmentResults_ai").doc(lastAssessmentId_ai).get();
      if (!assessmentDoc.exists) return null;

      const assessmentData = assessmentDoc.data();

      // Fallback learning path logic
      if (!learningPath) {
        if (assessmentData.metadata?.learningPath) {
          learningPath = assessmentData.metadata.learningPath;
          console.log("AI Skills - Fallback to assessment metadata:", learningPath);
        } else if (userData.currentPath) {
          learningPath = userData.currentPath;
          console.log("AI Skills - Fallback to userData.currentPath:", learningPath);
        }
      }

      return {
        report: {
          employeeName: `${userData.firstName} ${userData.lastName}`,
          role: userData.userRole,
          learningPath: learningPath,
          currentPath: learningPath,
          competencyAnalysis: assessmentData.competencyAnalysis,
          summary: assessmentData.analysisSummary,
          enrollmentStatus: userData.enrollmentStatus || "not_enrolled",
          email: userData.userEmail
        },
        recommendations:
          assessmentData.courseRecommendations?.map((rec) => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason
          })) || [],
        other_learning_paths_courses:
          assessmentData.otherPathRecommendations?.map((rec) => ({
            course: rec.courseName || rec.course,
            reason: rec.justification || rec.reason,
            learningPath: rec.learningPath
          })) || []
      };
    } catch (error) {
      console.error("Error fetching AI skills data:", error);
      return null;
    }
  },
  mapSectionToLearningPath(sectionNumber) {
    if (sectionNumber == null) return null;
    switch (Number(sectionNumber)) {
      case 1:
        return "Essentials";
      case 2:
        return "Intermediate";
      case 3:
        return "Advanced";
      case 4:
        return "Champions";
      default:
        return null;
    }
  },

  // ------------------------------
  // Skeleton Loader Helpers
  // ------------------------------
  showChartSkeleton(chartId) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;
    canvas.classList.add("loading");
    const container = canvas.closest(".chart-container");
    if (container) {
      container.classList.remove("loaded");
      const skeleton = container.querySelector(".chart-skeleton");
      if (skeleton) skeleton.classList.add("active");
    }
  },
  hideChartSkeleton(chartId) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;
    canvas.classList.remove("loading");
    const container = canvas.closest(".chart-container");
    if (container) {
      container.classList.add("loaded");
      const skeleton = container.querySelector(".chart-skeleton");
      if (skeleton) skeleton.classList.remove("active");
    }
  },
  showAllSkeletons() {
    ["roleDistributionChart", "completionChart", "skillGapChart", "recommendationsChart"].forEach((chartId) =>
      this.showChartSkeleton(chartId)
    );
  },
  hideAllSkeletons() {
    ["roleDistributionChart", "completionChart", "skillGapChart", "recommendationsChart"].forEach((chartId) =>
      this.hideChartSkeleton(chartId)
    );
  },

  // ------------------------------
  // Skill Filter Tabs for Charts
  // ------------------------------
  initializeSkillFilterTabs() {
    this.createSkillFilterTabs("skillGapChart", "Skill Gap Heatmap", (filter) => {
      this.currentSkillGapFilter = filter;
      this.updateSkillGapChartFiltered();
    });
    this.createSkillFilterTabs("recommendationsChart", "Top Course Recommendations", (filter) => {
      this.currentRecommendationFilter = filter;
      this.updateRecommendationsChartFiltered();
    });
  },
  createSkillFilterTabs(chartId, chartTitle, onFilterChange) {
    const chartContainer = document.getElementById(chartId)?.closest(".reports-card");
    if (!chartContainer) return;
    const titleElem = chartContainer.querySelector(".title-major");
    const tabsContainer = document.createElement("div");
    tabsContainer.className = "flex space-x-4 mt-2 mb-4 border-b border-gray-200";
    tabsContainer.setAttribute("data-filter-tabs", chartId);
    const combinedTab = this.createFilterTab("combined", "Combined", true);
    const digitalTab = this.createFilterTab("digital", "Digital Skills");
    const softTab = this.createFilterTab("soft", "Soft Skills");
    const aiTab = this.createFilterTab("ai", "AI Skills");
    [combinedTab, digitalTab, softTab, aiTab].forEach((tab) => {
      tab.addEventListener("click", () => {
        if (tab.classList.contains("active")) return;
        const tabs = tabsContainer.querySelectorAll(".skill-filter-tab");
        tab.classList.add("tab-switching");
        tabs.forEach((t) => {
          t.classList.remove("active");
          t.classList.add("text-gray-500");
          t.style.color = "";
          t.style.borderBottomColor = "";
        });
        tab.classList.add("active");
        tab.classList.remove("text-gray-500");
        tab.style.color = GREEN_COLORS.primary;
        tab.style.borderBottomColor = GREEN_COLORS.primary;
        onFilterChange(tab.dataset.filter);
        setTimeout(() => {
          tab.classList.remove("tab-switching");
        }, 400);
      });
    });
    tabsContainer.appendChild(combinedTab);
    tabsContainer.appendChild(digitalTab);
    tabsContainer.appendChild(softTab);
    tabsContainer.appendChild(aiTab);
    titleElem.insertAdjacentElement("afterend", tabsContainer);
    const style = document.head.querySelector("style") || document.createElement("style");
    style.textContent += `
      .skill-filter-tab {
        padding: 0.5rem 1rem;
        border-bottom: 2px solid transparent;
        font-size: 0.875rem;
        transition: color 0.2s, border-color 0.2s;
        cursor: pointer;
      }
      .skill-filter-tab.active {
        border-bottom-color: ${GREEN_COLORS.primary};
        color: ${GREEN_COLORS.primary};
      }
      .skill-filter-transition {
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
      }
      .skill-filter-transition.fade-out {
        opacity: 0;
        transform: translateY(10px);
      }
      .skill-filter-transition.fade-in {
        opacity: 1;
        transform: translateY(0);
      }
      .empty-data-message {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        color: #6b7280;
        font-style: italic;
        text-align: center;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      .empty-data-message.visible {
        opacity: 1;
      }
    `;
    if (!document.head.contains(style)) {
      document.head.appendChild(style);
    }
  },
  createFilterTab(filterValue, labelText, isActive = false) {
    const tab = document.createElement("button");
    tab.className = `skill-filter-tab ${isActive ? "active" : "text-gray-500"}`;
    if (isActive) {
      tab.style.color = GREEN_COLORS.primary;
      tab.style.borderBottomColor = GREEN_COLORS.primary;
    }
    tab.textContent = labelText;
    tab.dataset.filter = filterValue;
    return tab;
  },
  updateSkillGapChartFiltered() {
    const chartId = "skillGapChart";
    const chartContainer = document.getElementById(chartId)?.closest(".chart-container");
    if (chartContainer) chartContainer.classList.add("transitioning");
    this.showChartSkeleton(chartId);
    setTimeout(() => {
      let roleSkillData;
      switch (this.currentSkillGapFilter) {
        case "digital":
          roleSkillData = this.digitalRoleSkillGaps;
          break;
        case "soft":
          roleSkillData = this.softRoleSkillGaps;
          break;
        case "ai":
          roleSkillData = this.aiRoleSkillGaps;
          break;
        case "combined":
        default:
          roleSkillData = this.combineSkillGaps(this.digitalRoleSkillGaps, this.softRoleSkillGaps, this.aiRoleSkillGaps);
          break;
      }
      const hasData =
        Object.keys(roleSkillData).length > 0 &&
        Object.values(roleSkillData).some((r) => Object.keys(r).length > 0);
      if (hasData) {
        this.showChart(chartId);
        this.updateSkillGapChart(roleSkillData);
      } else {
        this.showEmptyState(chartId, `No ${this.getFilterName(this.currentSkillGapFilter)} data available`);
      }
      setTimeout(() => {
        this.hideChartSkeleton(chartId);
        if (chartContainer) {
          chartContainer.classList.remove("transitioning");
          chartContainer.classList.add("transitioned");
          setTimeout(() => {
            chartContainer.classList.remove("transitioned");
          }, 400);
        }
      }, 300);
    }, 300);
  },
  updateRecommendationsChartFiltered() {
    const chartId = "recommendationsChart";
    const chartContainer = document.getElementById(chartId)?.closest(".chart-container");
    if (chartContainer) chartContainer.classList.add("transitioning");
    this.showChartSkeleton(chartId);
    setTimeout(() => {
      let recommendationsData;
      switch (this.currentRecommendationFilter) {
        case "digital":
          recommendationsData = this.digitalCourseRecommendations;
          break;
        case "soft":
          recommendationsData = this.softCourseRecommendations;
          break;
        case "ai":
          recommendationsData = this.aiCourseRecommendations;
          break;
        case "combined":
        default:
          recommendationsData = this.combineCourseRecommendations(
            this.digitalCourseRecommendations,
            this.softCourseRecommendations,
            this.aiCourseRecommendations
          );
          break;
      }
      const hasData = Object.keys(recommendationsData).length > 0;
      if (hasData) {
        this.showChart(chartId);
        this.updateRecommendationsChart(recommendationsData);
      } else {
        this.showEmptyState(chartId, `No ${this.getFilterName(this.currentRecommendationFilter)} recommendations available`);
      }
      setTimeout(() => {
        this.hideChartSkeleton(chartId);
        if (chartContainer) {
          chartContainer.classList.remove("transitioning");
          chartContainer.classList.add("transitioned");
          setTimeout(() => {
            chartContainer.classList.remove("transitioned");
          }, 400);
        }
      }, 300);
    }, 300);
  },
  combineSkillGaps(digitalGaps, softGaps, aiGaps = {}) {
    const combined = {};
    Object.entries(digitalGaps).forEach(([role, skills]) => {
      combined[role] = { ...skills };
    });
    Object.entries(softGaps).forEach(([role, skills]) => {
      if (!combined[role]) combined[role] = {};
      Object.entries(skills).forEach(([skill, count]) => {
        combined[role][skill] = (combined[role][skill] || 0) + count;
      });
    });
    Object.entries(aiGaps).forEach(([role, skills]) => {
      if (!combined[role]) combined[role] = {};
      Object.entries(skills).forEach(([skill, count]) => {
        combined[role][skill] = (combined[role][skill] || 0) + count;
      });
    });
    return combined;
  },
  combineCourseRecommendations(digitalRecs, softRecs, aiRecs = {}) {
    const combined = { ...digitalRecs };
    Object.entries(softRecs).forEach(([course, count]) => {
      combined[course] = (combined[course] || 0) + count;
    });
    Object.entries(aiRecs).forEach(([course, count]) => {
      combined[course] = (combined[course] || 0) + count;
    });
    return combined;
  },
  showChart(chartId) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;
    canvas.style.display = "block";
    const container = canvas.closest(".chart-container");
    if (container) {
      const emptyMessage = container.querySelector(".empty-data-message");
      if (emptyMessage) {
        emptyMessage.style.opacity = "0";
        setTimeout(() => emptyMessage.remove(), 300);
      }
    }
  },
  showEmptyState(chartId, message) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;
    canvas.style.display = "none";
    const container = canvas.closest(".chart-container");
    if (!container) return;
    const existingMessage = container.querySelector(".empty-data-message");
    if (existingMessage) existingMessage.remove();
    const emptyMessage = document.createElement("div");
    emptyMessage.className = "empty-data-message";
    emptyMessage.textContent = message;
    container.appendChild(emptyMessage);
    setTimeout(() => emptyMessage.classList.add("visible"), 10);
  },
  getFilterName(filter) {
    switch (filter) {
      case "digital":
        return "digital skills";
      case "soft":
        return "soft skills";
      case "ai":
        return "AI skills";
      default:
        return "skills";
    }
  },

  // ------------------------------
  // Chart Export Methods
  // ------------------------------
  setupExportControls() {
    // Setup dropdown toggles
    this.setupExportDropdown('skillGapExportBtn', 'skillGapExportMenu');
    this.setupExportDropdown('recommendationsExportBtn', 'recommendationsExportMenu');

    // Setup export option handlers
    document.addEventListener('click', (e) => {
      if (e.target.closest('.export-option')) {
        const option = e.target.closest('.export-option');
        const format = option.dataset.format;
        const chartId = option.dataset.chart;
        this.exportChart(chartId, format);
      }
    });

    // Keyboard navigation for export options
    document.addEventListener('keydown', (e) => {
      if (e.target.closest('.export-option')) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          e.target.click();
        }
        
        // Arrow key navigation within dropdown
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();
          const currentMenu = e.target.closest('.export-dropdown-menu');
          const options = Array.from(currentMenu.querySelectorAll('.export-option'));
          const currentIndex = options.indexOf(e.target);
          
          let nextIndex;
          if (e.key === 'ArrowDown') {
            nextIndex = currentIndex + 1 >= options.length ? 0 : currentIndex + 1;
          } else {
            nextIndex = currentIndex - 1 < 0 ? options.length - 1 : currentIndex - 1;
          }
          
          // Update tabindex and focus
          options.forEach((option, index) => {
            option.setAttribute('tabindex', index === nextIndex ? '0' : '-1');
          });
          options[nextIndex].focus();
        }
      }
      
      // Close dropdown on Escape
      if (e.key === 'Escape') {
        document.querySelectorAll('.export-dropdown-menu').forEach(menu => {
          if (menu.classList.contains('show')) {
            menu.classList.remove('show');
            const button = document.querySelector(`[aria-controls="${menu.id}"]`);
            if (button) {
              button.setAttribute('aria-expanded', 'false');
              button.focus(); // Return focus to the button
            }
            // Reset tabindex
            menu.querySelectorAll('.export-option').forEach(option => {
              option.setAttribute('tabindex', '-1');
            });
          }
        });
      }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.export-dropdown-container')) {
        document.querySelectorAll('.export-dropdown-menu').forEach(menu => {
          menu.classList.remove('show');
          // Update aria-expanded for buttons
          const button = document.querySelector(`[aria-controls="${menu.id}"]`);
          if (button) button.setAttribute('aria-expanded', 'false');
          // Reset tabindex
          menu.querySelectorAll('.export-option').forEach(option => {
            option.setAttribute('tabindex', '-1');
          });
        });
      }
    });
  },

  setupExportDropdown(buttonId, menuId) {
    const button = document.getElementById(buttonId);
    const menu = document.getElementById(menuId);
    
    if (button && menu) {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        // Close other dropdowns
        document.querySelectorAll('.export-dropdown-menu').forEach(m => {
          if (m !== menu) {
            m.classList.remove('show');
            // Update aria-expanded for other buttons
            const otherButton = document.querySelector(`[aria-controls="${m.id}"]`);
            if (otherButton) otherButton.setAttribute('aria-expanded', 'false');
            // Reset tabindex for other menu items
            m.querySelectorAll('.export-option').forEach(option => {
              option.setAttribute('tabindex', '-1');
            });
          }
        });
        // Toggle this dropdown
        const isShown = menu.classList.toggle('show');
        button.setAttribute('aria-expanded', isShown.toString());
        
        // Manage focus and tabindex
        if (isShown) {
          // Set first item as focusable and focus it
          const firstOption = menu.querySelector('.export-option');
          if (firstOption) {
            firstOption.setAttribute('tabindex', '0');
            firstOption.focus();
          }
        } else {
          // Reset tabindex when closing
          menu.querySelectorAll('.export-option').forEach(option => {
            option.setAttribute('tabindex', '-1');
          });
        }
      });
    }
  },

  async exportChart(chartId, format) {
    const chart = this.charts[chartId === 'skillGapChart' ? 'skillGap' : 'recommendations'];
    const chartTitle = chartId === 'skillGapChart' ? 'Skills Gap Heatmap' : 'Top Course Recommendations';
    
    if (!chart) {
      console.error('Chart not found:', chartId);
      return;
    }

    try {
      // Show loading state
      const container = document.getElementById(chartId).closest('.chart-export-controls');
      if (container) container.classList.add('export-loading');

      const currentFilter = chartId === 'skillGapChart' ? this.currentSkillGapFilter : this.currentRecommendationFilter;
      const filterText = this.getFilterName(currentFilter);
      const filename = this.generateFilename(chartTitle, filterText, format);

      switch (format) {
        case 'png':
          await this.exportAsPNG(chart, filename, chartTitle, filterText);
          break;
        case 'svg':
          await this.exportAsSVG(chart, filename, chartTitle, filterText);
          break;
        case 'pdf':
          await this.exportAsPDF(chart, filename, chartTitle, filterText);
          break;
        case 'excel':
          await this.exportAsExcel(chartId, filename, chartTitle, filterText);
          break;
        case 'csv':
          await this.exportAsCSV(chartId, filename, chartTitle, filterText);
          break;
        default:
          console.error('Unsupported export format:', format);
      }

      // Close dropdown
      document.querySelectorAll('.export-dropdown-menu').forEach(menu => {
        menu.classList.remove('show');
        // Update aria-expanded for buttons
        const button = document.querySelector(`[aria-controls="${menu.id}"]`);
        if (button) button.setAttribute('aria-expanded', 'false');
      });

      // Show success notification if available
      if (typeof showNotification === 'function') {
        const formatNames = {
          'png': 'PNG image',
          'svg': 'SVG vector',
          'pdf': 'PDF document',
          'excel': 'Excel spreadsheet',
          'csv': 'CSV data file'
        };
        const formatName = formatNames[format] || format.toUpperCase();
        showNotification(`${chartTitle} exported successfully as ${formatName}`, 'success');
      }

    } catch (error) {
      console.error('Export error:', error);
      
      // Show error notification if available
      if (typeof showNotification === 'function') {
        showNotification('Failed to export chart. Please try again.', 'error');
      } else {
        alert('Failed to export chart. Please try again.');
      }
    } finally {
      // Remove loading state
      const container = document.getElementById(chartId).closest('.chart-export-controls');
      if (container) container.classList.remove('export-loading');
    }
  },

  async exportAsPNG(chart, filename, title, filterText) {
    const canvas = chart.canvas;
    const ctx = canvas.getContext('2d');
    
    // Create a temporary canvas with white background
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    
    // Fill with white background
    tempCtx.fillStyle = 'white';
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    
    // Draw the chart
    tempCtx.drawImage(canvas, 0, 0);
    
    // Convert to blob and download
    tempCanvas.toBlob((blob) => {
      this.downloadBlob(blob, filename);
    }, 'image/png', 1.0);
  },

  async exportAsSVG(chart, filename, title, filterText) {
    // For SVG export, we'll convert the canvas to SVG
    const canvas = chart.canvas;
    const ctx = canvas.getContext('2d');
    
    // Create SVG string with embedded PNG
    const dataURL = canvas.toDataURL('image/png');
    const svgContent = `
      <svg width="${canvas.width}" height="${canvas.height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="white"/>
        <image href="${dataURL}" width="${canvas.width}" height="${canvas.height}"/>
        <text x="10" y="25" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1a202c">${title}</text>
        <text x="10" y="45" font-family="Arial, sans-serif" font-size="12" fill="#4a5568">Filter: ${filterText}</text>
      </svg>
    `;
    
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    this.downloadBlob(blob, filename);
  },

  async exportAsPDF(chart, filename, title, filterText) {
    const { jsPDF } = window.jspdf;
    if (!jsPDF) {
      console.error('jsPDF library not loaded');
      return;
    }

    const pdf = new jsPDF('l', 'mm', 'a4'); // Landscape orientation
    
    // Check chart type to determine export format
    const isSkillsGap = title.toLowerCase().includes('skill gap');
    const isRecommendations = title.toLowerCase().includes('recommendation');
    
    if (isSkillsGap) {
      // Skills gap table-based PDF (existing code)
      const matrixData = this.getSkillGapMatrixData();
      
      if (!matrixData || matrixData.length === 0) {
        alert('No data available for PDF export');
        return;
      }

      // Add title
      pdf.setFontSize(20);
      pdf.setTextColor(26, 32, 44); // #1a202c
      pdf.text(title, pdf.internal.pageSize.getWidth() / 2, 25, { align: 'center' });
      
      // Add filter info
      pdf.setFontSize(12);
      pdf.setTextColor(74, 85, 104); // #4a5568
      pdf.text(`Filter: ${filterText}`, pdf.internal.pageSize.getWidth() / 2, 35, { align: 'center' });
      
      // Create the table
      const headers = matrixData[0];
      const rows = matrixData.slice(1);
      
      pdf.autoTable({
        head: [headers],
        body: rows,
        startY: 45,
        theme: 'grid',
        headStyles: {
          fillColor: [104, 198, 146], // Green primary color
          textColor: [255, 255, 255],
          fontSize: 10,
          halign: 'center'
        },
        bodyStyles: { 
          fontSize: 9,
          halign: 'center'
        },
        columnStyles: {
          0: { halign: 'left', cellWidth: 25 } // Role column left-aligned and wider
        },
        didParseCell: function(data) {
          // Color code cells based on gap severity
          if (data.section === 'body' && data.column.index > 0) {
            const value = parseInt(data.cell.text[0]) || 0;
            if (value > 0) {
              let fillColor;
              if (value <= 3) fillColor = [200, 230, 200]; // Light Green - Minor gap
              else if (value <= 6) fillColor = [144, 200, 144]; // Medium Green - Moderate gap
              else if (value <= 9) fillColor = [88, 160, 88]; // Dark Green - Significant gap
              else if (value <= 12) fillColor = [34, 120, 34]; // Very Dark Green - Severe gap
              else fillColor = [20, 80, 20]; // Darkest Green - Critical gap
              
              data.cell.styles.fillColor = fillColor;
              if (value > 6) {
                data.cell.styles.textColor = [255, 255, 255]; // White text for darker green backgrounds
              }
            }
          }
        },
        margin: { left: 15, right: 15 }
      });
      
      // Add legend
      const legendY = pdf.autoTable.previous.finalY + 15;
      pdf.setFontSize(12);
      pdf.setTextColor(26, 32, 44);
      pdf.text('Legend:', 15, legendY);
      
      const legendItems = [
        '0 users: No skill gap',
        '1-3 users: Minor gap', 
        '4-6 users: Moderate gap',
        '7-9 users: Significant gap',
        '10-12 users: Severe gap',
        '13+ users: Critical gap'
      ];
      
      pdf.setFontSize(10);
      pdf.setTextColor(74, 85, 104);
      legendItems.forEach((item, index) => {
        pdf.text(item, 15, legendY + 10 + (index * 5));
      });

    } else if (isRecommendations) {
      // Course recommendations table-based PDF
      const recommendationsData = this.getCourseRecommendationsData();
      
      if (!recommendationsData || recommendationsData.length === 0) {
        alert('No data available for PDF export');
        return;
      }

      // Add title
      pdf.setFontSize(20);
      pdf.setTextColor(26, 32, 44); // #1a202c
      pdf.text(title, pdf.internal.pageSize.getWidth() / 2, 25, { align: 'center' });
      
      // Add filter info
      pdf.setFontSize(12);
      pdf.setTextColor(74, 85, 104); // #4a5568
      pdf.text(`Filter: ${filterText}`, pdf.internal.pageSize.getWidth() / 2, 35, { align: 'center' });
      
      // Create the table
      const headers = ['Rank', 'Course Name', 'Recommendations', 'Percentage'];
      const rows = recommendationsData.map(item => [
        item.rank,
        item.course,
        item.recommendations,
        `${item.percentage}%`
      ]);
      
      pdf.autoTable({
        head: [headers],
        body: rows,
        startY: 45,
        theme: 'grid',
        headStyles: {
          fillColor: [104, 198, 146], // Green primary color
          textColor: [255, 255, 255],
          fontSize: 12,
          halign: 'center'
        },
        bodyStyles: { 
          fontSize: 11,
          halign: 'center'
        },
        columnStyles: {
          0: { halign: 'center', cellWidth: 20 }, // Rank
          1: { halign: 'left', cellWidth: 80 },   // Course Name
          2: { halign: 'center', cellWidth: 30 }, // Recommendations
          3: { halign: 'center', cellWidth: 25 }  // Percentage
        },
        didParseCell: function(data) {
          // Color code based on rank
          if (data.section === 'body') {
            const rank = parseInt(data.row.raw[0]);
            let fillColor;
            if (rank <= 3) fillColor = [232, 245, 232]; // Light green for top 3
            else if (rank <= 7) fillColor = [255, 243, 205]; // Light yellow for top 7
            else fillColor = [248, 249, 250]; // Light gray for others
            
            data.cell.styles.fillColor = fillColor;
          }
        },
        margin: { left: 20, right: 20 }
      });
      
      // Add summary
      const summaryY = pdf.autoTable.previous.finalY + 15;
      pdf.setFontSize(12);
      pdf.setTextColor(26, 32, 44);
      pdf.text('Summary:', 20, summaryY);
      
      const totalRecommendations = recommendationsData.reduce((sum, item) => sum + item.recommendations, 0);
      const topCourse = recommendationsData[0];
      
      pdf.setFontSize(10);
      pdf.setTextColor(74, 85, 104);
      pdf.text(`Total course recommendations: ${totalRecommendations}`, 20, summaryY + 10);
      pdf.text(`Most recommended course: ${topCourse.course} (${topCourse.recommendations} recommendations)`, 20, summaryY + 16);
      pdf.text(`Top 3 courses account for ${recommendationsData.slice(0, 3).reduce((sum, item) => sum + parseFloat(item.percentage), 0).toFixed(1)}% of all recommendations`, 20, summaryY + 22);
      
    } else {
      // For other charts, use the original image-based approach
      const canvas = chart.canvas;
      const imgData = canvas.toDataURL('image/png');
      
      // Calculate dimensions
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const aspectRatio = canvas.width / canvas.height;
      
      let imgWidth = pdfWidth - 40; // 20mm margin on each side
      let imgHeight = imgWidth / aspectRatio;
      
      if (imgHeight > pdfHeight - 80) { // Leave space for title and margins
        imgHeight = pdfHeight - 80;
        imgWidth = imgHeight * aspectRatio;
      }
      
      const x = (pdfWidth - imgWidth) / 2;
      const y = 40;
      
      // Add title
      pdf.setFontSize(20);
      pdf.setTextColor(26, 32, 44); // #1a202c
      pdf.text(title, pdfWidth / 2, 25, { align: 'center' });
      
      // Add filter info
      pdf.setFontSize(12);
      pdf.setTextColor(74, 85, 104); // #4a5568
      pdf.text(`Filter: ${filterText}`, pdfWidth / 2, 35, { align: 'center' });
      
      // Add chart image
      pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);
    }
    
    // Add footer with date and company
    pdf.setFontSize(10);
    pdf.setTextColor(107, 114, 128); // #6b7280
    const date = new Date().toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    pdf.text(`Generated on ${date} | ${this.userCompany}`, pdfWidth / 2, pdfHeight - 10, { align: 'center' });
    
    // Save the PDF
    pdf.save(filename);
  },

  async exportAsExcel(chartId, filename, title, filterText) {
    if (!window.XLSX) {
      console.error('XLSX library not loaded');
      return;
    }

    const isSkillsGap = chartId === 'skillGapChart';
    const data = isSkillsGap ? this.getSkillGapMatrixData() : this.getCourseRecommendationsData();
    
    if (!data || data.length === 0) {
      alert('No data available to export');
      return;
    }

    // Create workbook
    const wb = window.XLSX.utils.book_new();

    if (isSkillsGap) {
      // Skills Gap Matrix export (existing code)
      const ws = window.XLSX.utils.aoa_to_sheet(data);

      // Set column widths
      const colWidths = data[0].map((_, index) => {
        if (index === 0) return { wch: 20 }; // Role column wider
        return { wch: 15 }; // Skill columns
      });
      ws['!cols'] = colWidths;

      // Add styling to header row
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "68C692" } }, // Green primary color
        alignment: { horizontal: "center", vertical: "center" }
      };

      // Apply header styling
      for (let i = 0; i < data[0].length; i++) {
        const cellRef = window.XLSX.utils.encode_cell({ r: 0, c: i });
        if (!ws[cellRef]) ws[cellRef] = {};
        ws[cellRef].s = headerStyle;
      }

      // Add conditional formatting for gap severity
      for (let r = 1; r < data.length; r++) {
        for (let c = 1; c < data[r].length; c++) {
          const cellRef = window.XLSX.utils.encode_cell({ r, c });
          const value = data[r][c];
          
          if (typeof value === 'number' && value > 0) {
            let fillColor;
            if (value <= 3) fillColor = "C8E6C8"; // Light Green - Minor gap
            else if (value <= 6) fillColor = "90C890"; // Medium Green - Moderate gap
            else if (value <= 9) fillColor = "58A058"; // Dark Green - Significant gap
            else if (value <= 12) fillColor = "227822"; // Very Dark Green - Severe gap
            else fillColor = "145014"; // Darkest Green - Critical gap

            if (!ws[cellRef]) ws[cellRef] = {};
            ws[cellRef].s = {
              fill: { fgColor: { rgb: fillColor } },
              alignment: { horizontal: "center" },
              font: { color: { rgb: value > 6 ? "FFFFFF" : "000000" } } // White text for darker backgrounds
            };
          }
        }
      }

      window.XLSX.utils.book_append_sheet(wb, ws, 'Skills Gap Matrix');

    } else {
      // Course Recommendations export
      const headers = ['Rank', 'Course Name', 'Recommendations', 'Percentage'];
      const rows = data.map(item => [
        item.rank,
        item.course,
        item.recommendations,
        `${item.percentage}%`
      ]);

      const wsData = [headers, ...rows];
      const ws = window.XLSX.utils.aoa_to_sheet(wsData);

      // Set column widths
      ws['!cols'] = [
        { wch: 8 },  // Rank
        { wch: 40 }, // Course Name
        { wch: 15 }, // Recommendations
        { wch: 12 }  // Percentage
      ];

      // Add styling to header row
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "68C692" } }, // Green primary color
        alignment: { horizontal: "center", vertical: "center" }
      };

      // Apply header styling
      for (let i = 0; i < headers.length; i++) {
        const cellRef = window.XLSX.utils.encode_cell({ r: 0, c: i });
        if (!ws[cellRef]) ws[cellRef] = {};
        ws[cellRef].s = headerStyle;
      }

      // Add conditional formatting for recommendation counts
      for (let r = 1; r < wsData.length; r++) {
        // Color-code based on rank
        let fillColor;
        if (r <= 3) fillColor = "E8F5E8"; // Light green for top 3
        else if (r <= 7) fillColor = "FFF3CD"; // Light yellow for top 7
        else fillColor = "F8F9FA"; // Light gray for others

        for (let c = 0; c < headers.length; c++) {
          const cellRef = window.XLSX.utils.encode_cell({ r, c });
          if (!ws[cellRef]) ws[cellRef] = {};
          ws[cellRef].s = {
            fill: { fgColor: { rgb: fillColor } },
            alignment: { horizontal: c === 1 ? "left" : "center" } // Left align course names
          };
        }
      }

      window.XLSX.utils.book_append_sheet(wb, ws, 'Course Recommendations');
    }

    // Add metadata sheet
    const metaData = [
      [title + ' Report'],
      [''],
      ['Export Details:'],
      ['Title:', title],
      ['Filter:', filterText],
      ['Company:', this.userCompany],
      ['Generated:', new Date().toLocaleString()],
      ['']
    ];

    if (isSkillsGap) {
      metaData.push(
        ['Legend:'],
        ['0 users', 'No skill gap'],
        ['1-3 users', 'Minor gap'],
        ['4-6 users', 'Moderate gap'],
        ['7-9 users', 'Significant gap'],
        ['10-12 users', 'Severe gap'],
        ['13+ users', 'Critical gap']
      );
    } else {
      metaData.push(
        ['Color Coding:'],
        ['Green', 'Top 3 most recommended courses'],
        ['Yellow', 'Top 4-7 courses'],
        ['Gray', 'Other recommendations']
      );
    }

    const metaWs = window.XLSX.utils.aoa_to_sheet(metaData);
    metaWs['!cols'] = [{ wch: 20 }, { wch: 30 }];

    // Add sheets to workbook
    window.XLSX.utils.book_append_sheet(wb, metaWs, 'Report Info');

    // Save file
    window.XLSX.writeFile(wb, filename);
  },

  async exportAsCSV(chartId, filename, title, filterText) {
    const data = this.getSkillGapMatrixData();
    if (!data || data.length === 0) {
      alert('No data available to export');
      return;
    }

    // Convert data to CSV format
    const csvContent = data.map(row => 
      row.map(cell => {
        // Handle cells that might contain commas or quotes
        if (typeof cell === 'string' && (cell.includes(',') || cell.includes('"'))) {
          return `"${cell.replace(/"/g, '""')}"`;
        }
        return cell;
      }).join(',')
    ).join('\n');

    // Add metadata header
    const metadata = [
      `# ${title}`,
      `# Filter: ${filterText}`,
      `# Company: ${this.userCompany}`,
      `# Generated: ${new Date().toLocaleString()}`,
      `# `,
      `# Legend: 0=No gap, 1-3=Minor, 4-6=Moderate, 7-9=Significant, 10-12=Severe, 13+=Critical`,
      `#`
    ].join('\n') + '\n';

    const fullCsvContent = metadata + csvContent;

    // Create and download file
    const blob = new Blob([fullCsvContent], { type: 'text/csv;charset=utf-8;' });
    this.downloadBlob(blob, filename);
  },

  getSkillGapMatrixData() {
    // Get current skills gap data based on active filter
    let roleSkillData;
    switch (this.currentSkillGapFilter) {
      case "digital":
        roleSkillData = this.digitalRoleSkillGaps;
        break;
      case "soft":
        roleSkillData = this.softRoleSkillGaps;
        break;
      case "ai":
        roleSkillData = this.aiRoleSkillGaps;
        break;
      case "combined":
      default:
        roleSkillData = this.combineSkillGaps(
          this.digitalRoleSkillGaps, 
          this.softRoleSkillGaps, 
          this.aiRoleSkillGaps
        );
        break;
    }

    if (!roleSkillData || Object.keys(roleSkillData).length === 0) {
      return null;
    }

    // Get all roles and skills
    const roles = Object.keys(roleSkillData);
    const skills = [...new Set(
      Object.values(roleSkillData).flatMap(r => Object.keys(r))
    )].sort();

    // Create matrix data with headers
    const matrixData = [];
    
    // Header row
    matrixData.push(['Role', ...skills]);
    
    // Data rows
    roles.forEach(role => {
      const row = [role];
      skills.forEach(skill => {
        row.push(roleSkillData[role][skill] || 0);
      });
      matrixData.push(row);
    });

    return matrixData;
  },

  getCourseRecommendationsData() {
    // Get current recommendations data based on active filter
    let recommendationsData;
    switch (this.currentRecommendationFilter) {
      case "digital":
        recommendationsData = this.digitalCourseRecommendations;
        break;
      case "soft":
        recommendationsData = this.softCourseRecommendations;
        break;
      case "ai":
        recommendationsData = this.aiCourseRecommendations;
        break;
      case "combined":
      default:
        recommendationsData = this.combineCourseRecommendations(
          this.digitalCourseRecommendations,
          this.softCourseRecommendations,
          this.aiCourseRecommendations
        );
        break;
    }

    if (!recommendationsData || Object.keys(recommendationsData).length === 0) {
      return null;
    }

    // Convert to array format sorted by recommendation count
    const sortedEntries = Object.entries(recommendationsData)
      .sort(([, a], [, b]) => b - a)
      .map(([course, count], index) => ({
        rank: index + 1,
        course: course,
        recommendations: count,
        percentage: 0 // Will be calculated below
      }));

    // Calculate percentages
    const totalRecommendations = sortedEntries.reduce((sum, item) => sum + item.recommendations, 0);
    sortedEntries.forEach(item => {
      item.percentage = totalRecommendations > 0 ? ((item.recommendations / totalRecommendations) * 100).toFixed(1) : 0;
    });

    return sortedEntries;
  },

  generateFilename(chartTitle, filterText, format) {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const sanitizedTitle = chartTitle.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    const sanitizedFilter = filterText.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    
    // Map format to file extension
    const extensions = {
      'png': 'png',
      'svg': 'svg', 
      'pdf': 'pdf',
      'excel': 'xlsx',
      'csv': 'csv'
    };
    
    const extension = extensions[format] || format;
    return `${sanitizedTitle}-${sanitizedFilter}-${date}.${extension}`;
  },

  downloadBlob(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
};


  // Cleanup on page unload
  window.addEventListener('unload', () => {
    ReportsManager.cleanupCharts();
  });

  // Initialize once DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    ReportsManager.initialize();
    const firstTab = document.querySelector('.tab[data-tab="company-overview"]');
    if (firstTab) {
      ReportsManager.switchTabContent('company-overview');
    }
  });
